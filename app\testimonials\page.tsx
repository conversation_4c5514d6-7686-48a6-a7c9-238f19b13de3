"use client"

import Image from "next/image"
import { motion } from "framer-motion"
import { Card, CardContent } from "@/components/ui/card"
import { Star } from "lucide-react"

const testimonials = [
  {
    id: 1,
    name: "<PERSON><PERSON>",
    role: "Mert'in annesi (8 yaş)",
    quote:
      "Oğlum Mert'in teknolojiye olan ilgisi Uçuş Atölyesi ile bambaşka bir boyut kazandı. Artık evde sürekli yeni projeler geliştiriyor. Eğitmenler çok ilgili ve sabırlı. Kesinlikle tavsiye ediyorum.",
    image: "/testimonial-1.jpg",
    rating: 5,
    program: "UA1",
  },
  {
    id: 2,
    name: "<PERSON><PERSON>ü<PERSON>",
    role: "Def<PERSON>'nin babası (10 yaş)",
    quote:
      "Kızım Defne'nin özgüveni bu eğitimlerle çok arttı. Drone uçurmayı öğrenmek onu çok mutlu etti ve bilime olan ilgisini artırdı. Eğitim programı çok iyi planlanmış ve çocukların seviyesine uygun.",
    image: "/testimonial-2.jpg",
    rating: 5,
    program: "UA2",
  },
  {
    id: 3,
    name: "Elif Kara",
    role: "Can'ın annesi (12 yaş)",
    quote:
      "Eğitmenler çok ilgili ve sabırlı. Can'ın öğrenme sürecinde ona çok destek oldular. Oğlum artık kendi drone'unu programlayabiliyor ve bu onu çok heyecanlandırıyor. Kesinlikle tavsiye ediyorum.",
    image: "/testimonial-3.jpg",
    rating: 5,
    program: "UA2",
  },
  {
    id: 4,
    name: "Mehmet Aydın",
    role: "Zeynep'in babası (9 yaş)",
    quote:
      "Kızım Zeynep'in teknolojiye olan ilgisi sayesinde bu kursa kaydolduk ve çok memnun kaldık. Eğitmenler çok profesyonel ve çocuklarla iletişimleri harika. Zeynep her dersten sonra öğrendiklerini heyecanla anlatıyor.",
    image: "/testimonial-4.jpg",
    rating: 4,
    program: "UA1",
  },
  {
    id: 5,
    name: "Ayşe Demir",
    role: "Ali'nin annesi (13 yaş)",
    quote:
      "Oğlum Ali, UA3 programına katıldı ve inanılmaz bir deneyim yaşadı. 3D tasarım ve programlama konusunda kendini çok geliştirdi. Şimdi kendi projelerini geliştiriyor ve gelecekte mühendis olmak istiyor.",
    image: "/testimonial-5.jpg",
    rating: 5,
    program: "UA3",
  },
  {
    id: 6,
    name: "Hakan Yılmaz",
    role: "Ece'nin babası (11 yaş)",
    quote:
      "Kızım Ece, UA2 programına katıldı ve çok memnun kaldık. Eğitmenler çok ilgili ve sabırlı. Ece'nin problem çözme ve analitik düşünme becerileri çok gelişti. Kesinlikle tavsiye ediyorum.",
    image: "/testimonial-6.jpg",
    rating: 5,
    program: "UA2",
  },
  {
    id: 7,
    name: "Zeynep Kaya",
    role: "Burak'ın annesi (14 yaş)",
    quote:
      "Oğlum Burak, UA3 programına katıldı ve drone programlama konusunda kendini çok geliştirdi. Şimdi kendi projelerini geliştiriyor ve okul projelerinde bu bilgilerini kullanıyor. Eğitmenler çok profesyonel ve yardımsever.",
    image: "/testimonial-7.jpg",
    rating: 5,
    program: "UA3",
  },
  {
    id: 8,
    name: "Murat Şahin",
    role: "Deniz'in babası (7 yaş)",
    quote:
      "Oğlum Deniz, UA1 programına katıldı ve çok eğlendi. Drone teknolojisine olan ilgisi arttı ve artık bilim ve teknoloji konularına daha meraklı. Eğitmenler çocuklarla çok iyi iletişim kuruyorlar.",
    image: "/testimonial-8.jpg",
    rating: 4,
    program: "UA1",
  },
  {
    id: 9,
    name: "Canan Yıldırım",
    role: "Emre'nin annesi (12 yaş)",
    quote:
      "Oğlum Emre, UA2 programını tamamladı ve şimdi UA3'e devam ediyor. Drone teknolojisi ve programlama konusunda kendini çok geliştirdi. Eğitmenler çok bilgili ve çocukların seviyesine uygun eğitim veriyorlar.",
    image: "/testimonial-9.jpg",
    rating: 5,
    program: "UA2",
  },
]

export default function TestimonialsPage() {
  return (
    <div>
      {/* Hero Section */}
      <section className="relative py-20 bg-sky-600 text-white">
        <div className="container">
          <div className="max-w-3xl">
            <h1 className="text-4xl font-bold tracking-tight sm:text-5xl">Veli Yorumları</h1>
            <p className="mt-6 text-xl text-white/90">
              Çocuklarımızın eğitim sürecinde velilerimizin deneyimleri ve geri bildirimleri.
            </p>
          </div>
        </div>
      </section>

      {/* Testimonials */}
      <section className="py-20 bg-white">
        <div className="container">
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <motion.div
                key={testimonial.id}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <Card className="h-full">
                  <CardContent className="p-6">
                    <div className="flex items-center gap-4 mb-4">
                      <div className="relative h-16 w-16 overflow-hidden rounded-full">
                        <Image
                          src={testimonial.image || "/placeholder.svg"}
                          alt={testimonial.name}
                          fill
                          className="object-cover"
                        />
                      </div>
                      <div>
                        <h3 className="font-semibold">{testimonial.name}</h3>
                        <p className="text-sm text-gray-500">{testimonial.role}</p>
                        <div className="flex mt-1">
                          {[...Array(5)].map((_, i) => (
                            <Star
                              key={i}
                              className={`h-4 w-4 ${
                                i < testimonial.rating ? "text-yellow-400 fill-yellow-400" : "text-gray-300"
                              }`}
                            />
                          ))}
                        </div>
                      </div>
                    </div>
                    <div className="mb-4">
                      <span className="inline-block bg-sky-100 text-sky-800 text-xs px-2 py-1 rounded-full">
                        Program: {testimonial.program}
                      </span>
                    </div>
                    <p className="italic text-gray-600">"{testimonial.quote}"</p>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-sky-50">
        <div className="container">
          <div className="text-center max-w-3xl mx-auto">
            <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
              Siz de Deneyiminizi Paylaşın
            </h2>
            <p className="mt-4 text-lg text-gray-600">
              Çocuğunuz Uçuş Atölyesi'nde eğitim aldıysa, deneyiminizi bizimle paylaşın. Görüşleriniz bizim için
              değerli.
            </p>
            <p className="mt-2 text-gray-600">
              Yorumlarınızı <EMAIL> adresine gönderebilirsiniz.
            </p>
          </div>
        </div>
      </section>
    </div>
  )
}
