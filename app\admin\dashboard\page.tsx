"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Users, BookOpen, UserCircle, ImageIcon, TrendingUp } from "lucide-react"

export default function DashboardPage() {
  const [stats, setStats] = useState({
    registrations: { total: 0, pending: 0, approved: 0 },
    programs: { total: 0, active: 0 },
    instructors: { total: 0 },
    gallery: { total: 0 },
    testimonials: { total: 0 },
  })
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const fetchData = async () => {
      try {
        // Gerçek API çağrıları
        const [registrationsRes, programsRes, instructorsRes, galleryRes, testimonialsRes] = await Promise.all([
          fetch("/api/admin/registrations"),
          fetch("/api/admin/programs"),
          fetch("/api/admin/instructors"),
          fetch("/api/admin/gallery"),
          fetch("/api/admin/testimonials"),
        ])

        const registrationsData = await registrationsRes.json()
        const programsData = await programsRes.json()
        const instructorsData = await instructorsRes.json()
        const galleryData = await galleryRes.json()
        const testimonialsData = await testimonialsRes.json()

        if (registrationsData.success && programsData.success && instructorsData.success && galleryData.success && testimonialsData.success) {
          const registrations = registrationsData.data || []
          const programs = programsData.data || []
          const instructors = instructorsData.data || []
          const gallery = galleryData.data || []
          const testimonials = testimonialsData.data || []

          setStats({
            registrations: {
              total: registrations.length,
              pending: registrations.filter(r => r.status === "pending").length,
              approved: registrations.filter(r => r.status === "approved").length,
            },
            programs: {
              total: programs.length,
              active: programs.filter(p => p.isActive).length,
            },
            instructors: {
              total: instructors.length,
            },
            gallery: {
              total: gallery.length,
            },
            testimonials: {
              total: testimonials.length,
            },
          })
        } else {
          // API başarısız olursa mock data kullan
          setStats({
            registrations: { total: 45, pending: 12, approved: 33 },
            programs: { total: 3, active: 3 },
            instructors: { total: 6 },
            gallery: { total: 12 },
            testimonials: { total: 9 },
          })
        }
        setIsLoading(false)
      } catch (error) {
        console.error("Error fetching dashboard data:", error)
        // Hata durumunda mock data kullan
        setStats({
          registrations: { total: 45, pending: 12, approved: 33 },
          programs: { total: 3, active: 3 },
          instructors: { total: 6 },
          gallery: { total: 12 },
          testimonials: { total: 9 },
        })
        setIsLoading(false)
      }
    }

    fetchData()
  }, [])

  return (
    <div>
      <h1 className="text-3xl font-bold mb-8">Dashboard</h1>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Toplam Kayıt</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{isLoading ? "..." : stats.registrations.total}</div>
            <p className="text-xs text-muted-foreground">
              {isLoading ? "..." : `${stats.registrations.pending} bekleyen, ${stats.registrations.approved} onaylı`}
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Aktif Programlar</CardTitle>
            <BookOpen className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{isLoading ? "..." : stats.programs.active}</div>
            <p className="text-xs text-muted-foreground">
              {isLoading ? "..." : `Toplam ${stats.programs.total} program`}
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Eğitmenler</CardTitle>
            <UserCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{isLoading ? "..." : stats.instructors.total}</div>
            <p className="text-xs text-muted-foreground">Aktif eğitmen sayısı</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Galeri Öğeleri</CardTitle>
            <ImageIcon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{isLoading ? "..." : stats.gallery.total}</div>
            <p className="text-xs text-muted-foreground">Toplam galeri öğesi</p>
          </CardContent>
        </Card>
      </div>

      <div className="mt-8">
        <Tabs defaultValue="registrations">
          <TabsList>
            <TabsTrigger value="registrations">Son Kayıtlar</TabsTrigger>
            <TabsTrigger value="testimonials">Son Yorumlar</TabsTrigger>
          </TabsList>
          <TabsContent value="registrations" className="mt-4">
            <Card>
              <CardHeader>
                <CardTitle>Son Kayıtlar</CardTitle>
                <CardDescription>Son 5 kayıt başvurusu</CardDescription>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <p>Yükleniyor...</p>
                ) : (
                  <div className="space-y-4">
                    {[
                      {
                        id: 1,
                        childName: "Ahmet Yılmaz",
                        parentName: "Mehmet Yılmaz",
                        program: "UA1",
                        date: "2023-05-15",
                        status: "pending",
                      },
                      {
                        id: 2,
                        childName: "Ayşe Demir",
                        parentName: "Fatma Demir",
                        program: "UA2",
                        date: "2023-05-14",
                        status: "approved",
                      },
                      {
                        id: 3,
                        childName: "Can Kaya",
                        parentName: "Ali Kaya",
                        program: "UA1",
                        date: "2023-05-13",
                        status: "approved",
                      },
                      {
                        id: 4,
                        childName: "Deniz Şahin",
                        parentName: "Ayşe Şahin",
                        program: "UA3",
                        date: "2023-05-12",
                        status: "pending",
                      },
                      {
                        id: 5,
                        childName: "Elif Öztürk",
                        parentName: "Zeynep Öztürk",
                        program: "UA2",
                        date: "2023-05-11",
                        status: "approved",
                      },
                    ].map((registration) => (
                      <div key={registration.id} className="flex items-center justify-between border-b pb-2">
                        <div>
                          <p className="font-medium">{registration.childName}</p>
                          <p className="text-sm text-gray-500">
                            {registration.parentName} • {registration.program}
                          </p>
                        </div>
                        <div className="flex items-center gap-2">
                          <span className="text-sm text-gray-500">{registration.date}</span>
                          <span
                            className={`px-2 py-1 text-xs rounded-full ${
                              registration.status === "pending"
                                ? "bg-yellow-100 text-yellow-800"
                                : "bg-green-100 text-green-800"
                            }`}
                          >
                            {registration.status === "pending" ? "Bekliyor" : "Onaylandı"}
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
          <TabsContent value="testimonials" className="mt-4">
            <Card>
              <CardHeader>
                <CardTitle>Son Yorumlar</CardTitle>
                <CardDescription>Son 5 veli yorumu</CardDescription>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <p>Yükleniyor...</p>
                ) : (
                  <div className="space-y-4">
                    {[
                      {
                        id: 1,
                        name: "Selin Yıldız",
                        role: "Mert'in annesi",
                        quote: "Oğlum Mert'in teknolojiye olan ilgisi Uçuş Atölyesi ile bambaşka bir boyut kazandı.",
                        date: "2023-05-10",
                      },
                      {
                        id: 2,
                        name: "Kemal Öztürk",
                        role: "Defne'nin babası",
                        quote: "Kızım Defne'nin özgüveni bu eğitimlerle çok arttı.",
                        date: "2023-05-08",
                      },
                      {
                        id: 3,
                        name: "Elif Kara",
                        role: "Can'ın annesi",
                        quote: "Eğitmenler çok ilgili ve sabırlı. Can'ın öğrenme sürecinde ona çok destek oldular.",
                        date: "2023-05-05",
                      },
                      {
                        id: 4,
                        name: "Mehmet Aydın",
                        role: "Zeynep'in babası",
                        quote:
                          "Kızım Zeynep'in teknolojiye olan ilgisi sayesinde bu kursa kaydolduk ve çok memnun kaldık.",
                        date: "2023-05-03",
                      },
                      {
                        id: 5,
                        name: "Ayşe Demir",
                        role: "Ali'nin annesi",
                        quote: "Oğlum Ali, UA3 programına katıldı ve inanılmaz bir deneyim yaşadı.",
                        date: "2023-05-01",
                      },
                    ].map((testimonial) => (
                      <div key={testimonial.id} className="flex items-center justify-between border-b pb-2">
                        <div>
                          <p className="font-medium">{testimonial.name}</p>
                          <p className="text-sm text-gray-500">{testimonial.role}</p>
                          <p className="text-sm italic mt-1">"{testimonial.quote.substring(0, 60)}..."</p>
                        </div>
                        <span className="text-sm text-gray-500">{testimonial.date}</span>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>

      <div className="mt-8">
        <Card>
          <CardHeader>
            <CardTitle>Kayıt İstatistikleri</CardTitle>
            <CardDescription>Son 6 aylık kayıt verileri</CardDescription>
          </CardHeader>
          <CardContent className="h-[300px] flex items-center justify-center">
            <div className="text-center">
              <TrendingUp className="h-16 w-16 text-sky-500 mx-auto mb-4" />
              <p className="text-gray-500">Grafik burada görüntülenecek</p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
