"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import Image from "next/image"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { useToast } from "@/components/ui/use-toast"
import { PlusCircle, Pencil, Trash, Eye } from "lucide-react"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"

export default function GalleryPage() {
  const { toast } = useToast()
  const [galleryItems, setGalleryItems] = useState([])
  const [isLoading, setIsLoading] = useState(true)
  const [itemToDelete, setItemToDelete] = useState(null)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)

  useEffect(() => {
    const fetchData = async () => {
      try {
        // Simulate API call
        setTimeout(() => {
          setGalleryItems([
            {
              id: 1,
              title: "UA1 Sınıfı Drone Uçuşu",
              description: "Giriş seviyesi öğrencilerimizin ilk drone uçuş deneyimi",
              image: "/gallery-1.jpg",
              category: "ua1",
              isActive: true,
              createdAt: "2023-05-15T10:30:00Z",
            },
            {
              id: 2,
              title: "Drone Montaj Atölyesi",
              description: "Öğrencilerimiz kendi drone'larını monte ediyor",
              image: "/gallery-2.jpg",
              category: "workshop",
              isActive: true,
              createdAt: "2023-05-14T14:20:00Z",
            },
            {
              id: 3,
              title: "UA2 Programlama Dersi",
              description: "Orta seviye öğrencilerimiz drone programlama öğreniyor",
              image: "/gallery-3.jpg",
              category: "ua2",
              isActive: true,
              createdAt: "2023-05-13T09:15:00Z",
            },
            {
              id: 4,
              title: "Grup Çalışması",
              description: "Takım halinde proje geliştiren öğrencilerimiz",
              image: "/gallery-4.jpg",
              category: "teamwork",
              isActive: true,
              createdAt: "2023-05-12T16:45:00Z",
            },
            {
              id: 5,
              title: "UA3 İleri Seviye Proje",
              description: "İleri seviye öğrencilerimizin yarışma projesi",
              image: "/gallery-5.jpg",
              category: "ua3",
              isActive: true,
              createdAt: "2023-05-11T11:10:00Z",
            },
            {
              id: 6,
              title: "Mezuniyet Töreni",
              description: "Başarıyla programı tamamlayan öğrencilerimiz",
              image: "/gallery-6.jpg",
              category: "graduation",
              isActive: true,
              createdAt: "2023-05-10T15:30:00Z",
            },
          ])
          setIsLoading(false)
        }, 1000)
      } catch (error) {
        console.error("Error fetching gallery items:", error)
        setIsLoading(false)
      }
    }

    fetchData()
  }, [])

  const handleDeleteClick = (item) => {
    setItemToDelete(item)
    setIsDeleteDialogOpen(true)
  }

  const handleDeleteConfirm = async () => {
    try {
      // Simulate API call
      setTimeout(() => {
        setGalleryItems((prev) => prev.filter((item) => item.id !== itemToDelete.id))
        toast({
          title: "Galeri Öğesi Silindi",
          description: `"${itemToDelete.title}" başarıyla silindi.`,
        })
        setIsDeleteDialogOpen(false)
        setItemToDelete(null)
      }, 500)
    } catch (error) {
      console.error("Error deleting gallery item:", error)
      toast({
        title: "Hata",
        description: "Galeri öğesi silinirken bir hata oluştu.",
        variant: "destructive",
      })
    }
  }

  const formatDate = (dateString) => {
    const date = new Date(dateString)
    return new Intl.DateTimeFormat("tr-TR", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
    }).format(date)
  }

  const getCategoryName = (category) => {
    switch (category) {
      case "ua1":
        return "UA1 - Giriş Seviyesi"
      case "ua2":
        return "UA2 - Orta Seviye"
      case "ua3":
        return "UA3 - İleri Seviye"
      case "workshop":
        return "Atölye Çalışması"
      case "teamwork":
        return "Takım Çalışması"
      case "graduation":
        return "Mezuniyet"
      default:
        return category
    }
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-3xl font-bold">Galeri</h1>
        <Button className="bg-sky-500 hover:bg-sky-600" asChild>
          <Link href="/admin/gallery/new">
            <PlusCircle className="h-4 w-4 mr-2" />
            Yeni Fotoğraf
          </Link>
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Galeri Listesi</CardTitle>
          <CardDescription>Tüm galeri öğelerini görüntüleyin ve yönetin.</CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="text-center py-8">
              <p>Yükleniyor...</p>
            </div>
          ) : galleryItems.length === 0 ? (
            <div className="text-center py-8">
              <p>Henüz galeri öğesi bulunmuyor.</p>
              <Button className="mt-4 bg-sky-500 hover:bg-sky-600" asChild>
                <Link href="/admin/gallery/new">
                  <PlusCircle className="h-4 w-4 mr-2" />
                  İlk Fotoğrafı Ekle
                </Link>
              </Button>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {galleryItems.map((item) => (
                <Card key={item.id} className="overflow-hidden">
                  <div className="relative h-48">
                    <Image
                      src={item.image}
                      alt={item.title}
                      fill
                      className="object-cover"
                    />
                  </div>
                  <CardContent className="p-4">
                    <h3 className="font-semibold text-lg mb-2">{item.title}</h3>
                    <p className="text-sm text-gray-600 mb-2">{item.description}</p>
                    <div className="flex justify-between items-center text-xs text-gray-500">
                      <span className="bg-sky-100 text-sky-800 px-2 py-1 rounded">
                        {getCategoryName(item.category)}
                      </span>
                      <span>{formatDate(item.createdAt)}</span>
                    </div>
                  </CardContent>
                  <CardFooter className="p-4 pt-0">
                    <div className="flex justify-end gap-2 w-full">
                      <Button variant="ghost" size="sm" asChild>
                        <Link href={`/gallery`} target="_blank">
                          <Eye className="h-4 w-4" />
                          <span className="sr-only">Görüntüle</span>
                        </Link>
                      </Button>
                      <Button variant="ghost" size="sm" asChild>
                        <Link href={`/admin/gallery/${item.id}/edit`}>
                          <Pencil className="h-4 w-4" />
                          <span className="sr-only">Düzenle</span>
                        </Link>
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="text-red-500 hover:text-red-700 hover:bg-red-50"
                        onClick={() => handleDeleteClick(item)}
                      >
                        <Trash className="h-4 w-4" />
                        <span className="sr-only">Sil</span>
                      </Button>
                    </div>
                  </CardFooter>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
        <CardFooter className="flex justify-between">
          <p className="text-sm text-gray-500">Toplam {galleryItems.length} öğe</p>
        </CardFooter>
      </Card>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Galeri Öğesini Silmek İstediğinize Emin misiniz?</AlertDialogTitle>
            <AlertDialogDescription>
              Bu işlem geri alınamaz. Bu galeri öğesi kalıcı olarak silinecektir.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>İptal</AlertDialogCancel>
            <AlertDialogAction className="bg-red-500 hover:bg-red-600" onClick={handleDeleteConfirm}>
              Sil
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}
