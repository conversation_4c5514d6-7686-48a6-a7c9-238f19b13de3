"use client"

import { useParams } from "next/navigation"
import Link from "next/link"
import Image from "next/image"
import { motion } from "framer-motion"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"
import { CheckCircle, Clock, Users, Calendar, DollarSign } from "lucide-react"

const programs = {
  ua1: {
    title: "UA1 - Giriş Seviyesi",
    description: "Drone teknolojisine ilk adım. Temel uçuş prensipleri, güvenlik kuralları ve basit kontroller.",
    fullDescription:
      "UA1 programı, çocukların drone teknolojisi ile ilk tanışmalarını sağlayan giriş seviyesi bir eğitimdir. Bu programda, öğrenciler drone'ların temel parçalarını, çalışma prensiplerini ve güvenli uçuş kurallarını öğrenirler. Eğlenceli ve interaktif bir ortamda, temel uçuş manevralarını ve kontrolleri pratik yaparak geliştirirler.",
    age: "6-8 yaş",
    duration: "8 Hafta (Haftada 2 saat)",
    schedule: "Cumartesi 10:00-12:00 veya Pazar 13:00-15:00",
    price: "3.500 TL",
    image: "/program-ua1-detail.jpg",
    icon: "🚁",
    features: [
      "Drone'un temel parçaları ve çalışma prensipleri",
      "Güvenli uçuş kuralları ve düzenlemeler",
      "Temel uçuş manevraları ve kontroller",
      "Basit görevler ve oyunlar",
      "Takım çalışması ve iletişim becerileri",
    ],
    curriculum: [
      {
        week: "1. Hafta",
        title: "Tanışma ve Drone'a Giriş",
        content: "Tanışma etkinlikleri, drone teknolojisine giriş, temel kavramlar ve güvenlik kuralları.",
      },
      {
        week: "2. Hafta",
        title: "Drone Parçaları ve Çalışma Prensipleri",
        content: "Drone'un ana bileşenleri, motorlar, pervaneler, batarya ve kontrol sistemleri.",
      },
      {
        week: "3. Hafta",
        title: "İlk Uçuş Denemeleri",
        content: "Simülatör üzerinde uçuş pratikleri, temel kontroller ve manevralar.",
      },
      {
        week: "4. Hafta",
        title: "Temel Uçuş Teknikleri",
        content: "Kalkış, iniş, havada durma ve basit yön değiştirme hareketleri.",
      },
      {
        week: "5. Hafta",
        title: "Drone ile Oyunlar",
        content: "Eğlenceli oyunlar ve görevler ile uçuş becerilerini geliştirme.",
      },
      {
        week: "6. Hafta",
        title: "Takım Çalışması",
        content: "Grup halinde drone görevleri ve iletişim becerilerini geliştirme.",
      },
      {
        week: "7. Hafta",
        title: "Mini Projeler",
        content: "Basit drone projeleri geliştirme ve uygulama.",
      },
      {
        week: "8. Hafta",
        title: "Final Gösterisi ve Sertifika",
        content: "Öğrenilen becerilerin sergilendiği final gösterisi ve sertifika töreni.",
      },
    ],
    faqs: [
      {
        question: "Çocuğumun daha önce drone deneyimi olması gerekiyor mu?",
        answer: "Hayır, UA1 programı hiçbir ön bilgi gerektirmez. Tüm konular sıfırdan anlatılmaktadır.",
      },
      {
        question: "Drone'lar çocuklar için güvenli mi?",
        answer:
          "Evet, eğitimlerimizde çocuklar için özel tasarlanmış, güvenlik özellikleri olan drone'lar kullanılmaktadır. Ayrıca, tüm uçuşlar eğitmenler gözetiminde gerçekleştirilmektedir.",
      },
      {
        question: "Çocuğum kendi drone'unu getirmeli mi?",
        answer: "Hayır, tüm ekipmanlar tarafımızdan sağlanmaktadır. Ekstra bir maliyeti yoktur.",
      },
      {
        question: "Eğitim sonunda çocuğum neler öğrenmiş olacak?",
        answer:
          "Çocuğunuz drone'un temel parçalarını, güvenli uçuş kurallarını, temel uçuş manevralarını öğrenmiş ve takım çalışması becerilerini geliştirmiş olacaktır.",
      },
      {
        question: "Devamsızlık durumunda telafi dersi yapılıyor mu?",
        answer:
          "Evet, önceden bildirilen devamsızlıklar için telafi dersleri düzenlenmektedir. Detaylar için bizimle iletişime geçebilirsiniz.",
      },
    ],
  },
  ua2: {
    title: "UA2 - Orta Seviye",
    description: "Daha karmaşık uçuş teknikleri, basit kodlama ve drone parçalarının tanıtımı.",
    fullDescription:
      "UA2 programı, drone teknolojisinde bir adım daha ileri gitmek isteyen öğrenciler için tasarlanmıştır. Bu programda, öğrenciler daha karmaşık uçuş tekniklerini, basit kodlama prensiplerini ve drone parçalarının detaylı incelemesini öğrenirler. Ayrıca, fotoğraf ve video çekim teknikleri ile yaratıcı projeler geliştirirler.",
    age: "9-11 yaş",
    duration: "10 Hafta (Haftada 2 saat)",
    schedule: "Cumartesi 13:00-15:00 veya Çarşamba 16:00-18:00",
    price: "4.500 TL",
    image: "/program-ua2-detail.jpg",
    icon: "⚙️",
    features: [
      "İleri uçuş teknikleri ve manevralar",
      "Drone parçalarının detaylı incelenmesi",
      "Basit kodlama ve otonom uçuş",
      "Hava koşullarına göre uçuş stratejileri",
      "Fotoğraf ve video çekim teknikleri",
    ],
    curriculum: [
      {
        week: "1. Hafta",
        title: "UA1 Tekrarı ve İleri Seviye Giriş",
        content: "UA1'de öğrenilen konuların tekrarı ve UA2 programına giriş.",
      },
      {
        week: "2. Hafta",
        title: "İleri Uçuş Teknikleri",
        content: "Karmaşık manevralar, hız kontrolü ve hassas konumlandırma.",
      },
      {
        week: "3. Hafta",
        title: "Drone Parçaları ve Bakım",
        content: "Drone parçalarının detaylı incelenmesi, bakım ve onarım teknikleri.",
      },
      {
        week: "4. Hafta",
        title: "Kodlamaya Giriş",
        content: "Temel programlama kavramları ve drone kodlamasına giriş.",
      },
      {
        week: "5. Hafta",
        title: "Basit Otonom Uçuş",
        content: "Önceden programlanmış rotalar ve basit otonom uçuş görevleri.",
      },
      {
        week: "6. Hafta",
        title: "Hava Koşulları ve Uçuş",
        content: "Farklı hava koşullarında uçuş teknikleri ve stratejileri.",
      },
      {
        week: "7. Hafta",
        title: "Drone Fotoğrafçılığı",
        content: "Havadan fotoğraf ve video çekim teknikleri, kompozisyon.",
      },
      {
        week: "8. Hafta",
        title: "Video Düzenleme",
        content: "Çekilen görüntülerin düzenlenmesi ve basit video projesi.",
      },
      {
        week: "9. Hafta",
        title: "Proje Geliştirme",
        content: "Grup halinde drone projesi geliştirme ve uygulama.",
      },
      {
        week: "10. Hafta",
        title: "Proje Sunumu ve Sertifika",
        content: "Geliştirilen projelerin sunumu ve sertifika töreni.",
      },
    ],
    faqs: [
      {
        question: "UA1 programını tamamlamadan UA2'ye katılabilir miyiz?",
        answer:
          "UA1 programını tamamlamış olmak tercih edilir, ancak çocuğunuzun drone deneyimi varsa bir değerlendirme sonrası UA2'ye kabul edilebilir.",
      },
      {
        question: "Kodlama için ön bilgi gerekiyor mu?",
        answer: "Hayır, kodlama konuları sıfırdan anlatılmaktadır. Herhangi bir ön bilgi gerekmez.",
      },
      {
        question: "Çocuğum kendi drone'unu almalı mı?",
        answer:
          "Eğitim için gerekli tüm ekipmanlar tarafımızdan sağlanmaktadır. Ancak, evde pratik yapmak isteyen öğrenciler için uygun drone modelleri önerebiliriz.",
      },
      {
        question: "Çekilen fotoğraf ve videolar paylaşılacak mı?",
        answer:
          "Evet, öğrencilerin çektiği fotoğraf ve videolar eğitim sonunda kendilerine dijital olarak verilecekt  öğrencilerin çektiği fotoğraf ve videolar eğitim sonunda kendilerine dijital olarak verilecektir. Ayrıca veli izni olan öğrencilerin çalışmaları sosyal medya hesaplarımızda da paylaşılabilir.",
      },
      {
        question: "Eğitim dışında pratik yapma imkanı var mı?",
        answer:
          "Evet, belirli günlerde atölyemiz ek pratik saatleri için öğrencilerimize açıktır. Bu saatler için ek ücret alınmamaktadır.",
      },
    ],
  },
  ua3: {
    title: "UA3 - İleri Seviye",
    description: "Drone programlama, 3D tasarım, yarışma hazırlıkları ve proje geliştirme.",
    fullDescription:
      "UA3 programı, drone teknolojisinde uzmanlaşmak isteyen öğrenciler için tasarlanmış ileri seviye bir eğitimdir. Bu programda, öğrenciler ileri seviye programlama, 3D tasarım, sensör entegrasyonu ve veri analizi gibi konuları öğrenirler. Ayrıca, ulusal ve uluslararası yarışmalara hazırlık yaparak kapsamlı projeler geliştirirler.",
    age: "12-14 yaş",
    duration: "12 Hafta (Haftada 3 saat)",
    schedule: "Pazar 10:00-13:00 veya Salı ve Perşembe 16:00-17:30",
    price: "5.500 TL",
    image: "/program-ua3-detail.jpg",
    icon: "💻",
    features: [
      "İleri seviye programlama ve otonom uçuş",
      "3D tasarım ve drone parçaları üretimi",
      "Sensör entegrasyonu ve veri analizi",
      "Yarışma stratejileri ve hazırlık",
      "Kapsamlı proje geliştirme ve sunum",
    ],
    curriculum: [
      {
        week: "1. Hafta",
        title: "UA2 Tekrarı ve İleri Seviye Giriş",
        content: "UA2'de öğrenilen konuların tekrarı ve UA3 programına giriş.",
      },
      {
        week: "2. Hafta",
        title: "İleri Seviye Programlama",
        content: "Python ile drone programlama ve algoritma geliştirme.",
      },
      {
        week: "3. Hafta",
        title: "Otonom Uçuş Sistemleri",
        content: "Gelişmiş otonom uçuş sistemleri ve navigasyon algoritmaları.",
      },
      {
        week: "4. Hafta",
        title: "3D Tasarıma Giriş",
        content: "3D modelleme yazılımları ve drone parçaları tasarımı.",
      },
      {
        week: "5. Hafta",
        title: "3D Baskı ve Üretim",
        content: "Tasarlanan parçaların 3D yazıcıda basılması ve montajı.",
      },
      {
        week: "6. Hafta",
        title: "Sensör Entegrasyonu",
        content: "Farklı sensörlerin drone'a entegrasyonu ve programlanması.",
      },
      {
        week: "7. Hafta",
        title: "Veri Toplama ve Analiz",
        content: "Drone ile veri toplama, işleme ve analiz teknikleri.",
      },
      {
        week: "8. Hafta",
        title: "Yarışma Stratejileri",
        content: "Ulusal ve uluslararası drone yarışmaları ve hazırlık stratejileri.",
      },
      {
        week: "9-10. Hafta",
        title: "Proje Geliştirme",
        content: "Kapsamlı drone projesi geliştirme, planlama ve uygulama.",
      },
      {
        week: "11. Hafta",
        title: "Proje Tamamlama",
        content: "Projelerin son rötuşları ve sunum hazırlıkları.",
      },
      {
        week: "12. Hafta",
        title: "Proje Sunumu ve Sertifika",
        content: "Geliştirilen projelerin sunumu, değerlendirme ve sertifika töreni.",
      },
    ],
    faqs: [
      {
        question: "UA2 programını tamamlamadan UA3'e katılabilir miyiz?",
        answer:
          "UA2 programını tamamlamış olmak tercih edilir, ancak çocuğunuzun yeterli drone ve kodlama deneyimi varsa bir değerlendirme sonrası UA3'e kabul edilebilir.",
      },
      {
        question: "Programlama için bilgisayar gerekiyor mu?",
        answer:
          "Atölyemizde bilgisayarlar mevcuttur, ancak öğrencilerin kendi dizüstü bilgisayarlarını getirmeleri tavsiye edilir.",
      },
      {
        question: "3D tasarım için özel bir yazılım öğrenilecek mi?",
        answer:
          "Evet, eğitimde Fusion 360 ve TinkerCAD gibi 3D tasarım yazılımları öğretilecektir. Bu yazılımların eğitim sürümlerini ücretsiz olarak kullanabilirsiniz.",
      },
      {
        question: "Yarışmalara katılım zorunlu mu?",
        answer:
          "Hayır, yarışmalara katılım zorunlu değildir. Ancak, ilgi duyan öğrenciler için yarışma hazırlıkları ve mentorluk desteği sağlanmaktadır.",
      },
      {
        question: "Eğitim sonrası destek sağlanıyor mu?",
        answer:
          "Evet, UA3 programını tamamlayan öğrencilerimize mezuniyet sonrası da proje geliştirme ve yarışma hazırlıkları için destek sağlanmaktadır.",
      },
    ],
  },
}

export default function ProgramDetailPage() {
  const params = useParams()
  const slug = params.slug as string
  const program = programs[slug]

  if (!program) {
    return (
      <div className="container py-20 text-center">
        <h1 className="text-3xl font-bold mb-4">Program Bulunamadı</h1>
        <p className="mb-8">Aradığınız program mevcut değil veya kaldırılmış olabilir.</p>
        <Button asChild>
          <Link href="/programs">Tüm Programları Gör</Link>
        </Button>
      </div>
    )
  }

  return (
    <div>
      {/* Hero Section */}
      <section className="relative py-20 bg-sky-600 text-white">
        <div className="container">
          <div className="grid md:grid-cols-2 gap-12 items-center">
            <div>
              <div className="text-4xl mb-4">{program.icon}</div>
              <h1 className="text-4xl font-bold tracking-tight sm:text-5xl">{program.title}</h1>
              <p className="mt-6 text-xl text-white/90">{program.description}</p>
              <div className="mt-8 grid grid-cols-2 gap-4">
                <div className="flex items-center gap-2">
                  <Users className="h-5 w-5 text-sky-300" />
                  <span>Yaş Grubu: {program.age}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Clock className="h-5 w-5 text-sky-300" />
                  <span>Süre: {program.duration}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Calendar className="h-5 w-5 text-sky-300" />
                  <span>Program: {program.schedule}</span>
                </div>
                <div className="flex items-center gap-2">
                  <DollarSign className="h-5 w-5 text-sky-300" />
                  <span>Ücret: {program.price}</span>
                </div>
              </div>
              <div className="mt-10 flex flex-col sm:flex-row gap-4">
                <Button size="lg" variant="outline" className="text-white border-white hover:bg-white/10" asChild>
                  <Link href="/register">Şimdi Kayıt Ol</Link>
                </Button>
              </div>
            </div>
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5 }}
              className="relative h-[300px] md:h-[400px] rounded-lg overflow-hidden"
            >
              <Image src={program.image || "/placeholder.svg"} alt={program.title} fill className="object-cover" />
            </motion.div>
          </div>
        </div>
      </section>

      {/* Program Description */}
      <section className="py-20 bg-white">
        <div className="container">
          <div className="max-w-3xl mx-auto">
            <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl mb-8">Program Hakkında</h2>
            <p className="text-lg text-gray-600 mb-8">{program.fullDescription}</p>

            <h3 className="text-2xl font-bold mb-4">Program İçeriği</h3>
            <ul className="space-y-4 mb-8">
              {program.features.map((feature, index) => (
                <motion.li
                  key={index}
                  initial={{ opacity: 0, x: -20 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="flex items-start gap-2"
                >
                  <CheckCircle className="h-5 w-5 text-sky-500 mt-1 flex-shrink-0" />
                  <span>{feature}</span>
                </motion.li>
              ))}
            </ul>
          </div>
        </div>
      </section>

      {/* Curriculum */}
      <section className="py-20 bg-sky-50">
        <div className="container">
          <div className="max-w-3xl mx-auto">
            <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl mb-8 text-center">
              Haftalık Program
            </h2>
            <div className="space-y-4">
              {program.curriculum.map((week, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.05 }}
                  viewport={{ once: true }}
                >
                  <Card>
                    <CardContent className="p-6">
                      <div className="flex flex-col md:flex-row md:items-center gap-4">
                        <div className="font-bold text-sky-600 md:w-24 flex-shrink-0">{week.week}</div>
                        <div>
                          <h3 className="font-semibold text-lg">{week.title}</h3>
                          <p className="text-gray-600 mt-1">{week.content}</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* FAQs */}
      <section className="py-20 bg-white">
        <div className="container">
          <div className="max-w-3xl mx-auto">
            <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl mb-8 text-center">
              Sıkça Sorulan Sorular
            </h2>
            <Accordion type="single" collapsible className="w-full">
              {program.faqs.map((faq, index) => (
                <AccordionItem key={index} value={`item-${index}`}>
                  <AccordionTrigger className="text-left">{faq.question}</AccordionTrigger>
                  <AccordionContent>{faq.answer}</AccordionContent>
                </AccordionItem>
              ))}
            </Accordion>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-sky-600 text-white">
        <div className="container">
          <div className="text-center max-w-3xl mx-auto">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">Bu Programa Kayıt Olun</h2>
            <p className="mt-4 text-xl text-white/90">
              Çocuğunuzun drone teknolojisi ve havacılık bilimi yolculuğu başlasın.
            </p>
            <div className="mt-10 flex justify-center">
              <Button size="lg" variant="outline" className="text-white border-white hover:bg-white/10" asChild>
                <Link href="/register">Şimdi Kayıt Ol</Link>
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}
