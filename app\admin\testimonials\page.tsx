"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { useToast } from "@/components/ui/use-toast"
import { PlusCircle, Pencil, Trash, Eye, Star } from "lucide-react"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"

export default function TestimonialsPage() {
  const { toast } = useToast()
  const [testimonials, setTestimonials] = useState([])
  const [isLoading, setIsLoading] = useState(true)
  const [testimonialToDelete, setTestimonialToDelete] = useState(null)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)

  const fetchTestimonials = async () => {
    try {
      setIsLoading(true)
      const response = await fetch("/api/admin/testimonials")
      const data = await response.json()

      if (data.success) {
        setTestimonials(data.data)
      } else {
        console.error("Error fetching testimonials:", data.error)
        toast({
          title: "Hata",
          description: "Yorumlar yüklenirken bir hata oluştu.",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("Error fetching testimonials:", error)
      toast({
        title: "Hata",
        description: "Yorumlar yüklenirken bir hata oluştu.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchTestimonials()
  }, [])

  const handleDeleteClick = (testimonial) => {
    setTestimonialToDelete(testimonial)
    setIsDeleteDialogOpen(true)
  }

  const handleDeleteConfirm = async () => {
    try {
      const response = await fetch(`/api/admin/testimonials/${testimonialToDelete._id || testimonialToDelete.id}`, {
        method: "DELETE",
      })

      const data = await response.json()

      if (data.success) {
        setTestimonials((prev) => prev.filter((t) => (t._id || t.id) !== (testimonialToDelete._id || testimonialToDelete.id)))
        toast({
          title: "Yorum Silindi",
          description: `"${testimonialToDelete.name}" yorumu başarıyla silindi.`,
        })
        setIsDeleteDialogOpen(false)
        setTestimonialToDelete(null)
      } else {
        toast({
          title: "Hata",
          description: data.error || "Yorum silinirken bir hata oluştu.",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("Error deleting testimonial:", error)
      toast({
        title: "Hata",
        description: "Yorum silinirken bir hata oluştu.",
        variant: "destructive",
      })
    }
  }

  const formatDate = (dateString) => {
    const date = new Date(dateString)
    return new Intl.DateTimeFormat("tr-TR", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
    }).format(date)
  }

  const renderStars = (rating) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`h-4 w-4 ${i < rating ? "fill-yellow-400 text-yellow-400" : "text-gray-300"}`}
      />
    ))
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-3xl font-bold">Yorumlar</h1>
        <Button className="bg-sky-500 hover:bg-sky-600" asChild>
          <Link href="/admin/testimonials/new">
            <PlusCircle className="h-4 w-4 mr-2" />
            Yeni Yorum
          </Link>
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Yorum Listesi</CardTitle>
          <CardDescription>Tüm veli yorumlarını görüntüleyin ve yönetin.</CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="text-center py-8">
              <p>Yükleniyor...</p>
            </div>
          ) : testimonials.length === 0 ? (
            <div className="text-center py-8">
              <p>Henüz yorum bulunmuyor.</p>
              <Button className="mt-4 bg-sky-500 hover:bg-sky-600" asChild>
                <Link href="/admin/testimonials/new">
                  <PlusCircle className="h-4 w-4 mr-2" />
                  İlk Yorumu Ekle
                </Link>
              </Button>
            </div>
          ) : (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Fotoğraf</TableHead>
                    <TableHead>Ad Soyad</TableHead>
                    <TableHead>Rol</TableHead>
                    <TableHead>Yorum</TableHead>
                    <TableHead>Puan</TableHead>
                    <TableHead>Tarih</TableHead>
                    <TableHead>Durum</TableHead>
                    <TableHead className="text-right">İşlemler</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {testimonials.map((testimonial) => (
                    <TableRow key={testimonial._id || testimonial.id}>
                      <TableCell>
                        <div className="w-10 h-10 relative rounded-full overflow-hidden">
                          <Image
                            src={testimonial.image || "/placeholder-user.jpg"}
                            alt={testimonial.name}
                            fill
                            className="object-cover"
                          />
                        </div>
                      </TableCell>
                      <TableCell className="font-medium">{testimonial.name}</TableCell>
                      <TableCell>{testimonial.role}</TableCell>
                      <TableCell className="max-w-xs">
                        <p className="truncate">{testimonial.quote}</p>
                      </TableCell>
                      <TableCell>
                        <div className="flex">{renderStars(testimonial.rating)}</div>
                      </TableCell>
                      <TableCell>{formatDate(testimonial.createdAt)}</TableCell>
                      <TableCell>
                        <span
                          className={`px-2 py-1 text-xs rounded-full ${
                            testimonial.isActive ? "bg-green-100 text-green-800" : "bg-gray-100 text-gray-800"
                          }`}
                        >
                          {testimonial.isActive ? "Aktif" : "Pasif"}
                        </span>
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end gap-2">
                          <Button variant="ghost" size="sm" asChild>
                            <Link href={`/testimonials`} target="_blank">
                              <Eye className="h-4 w-4" />
                              <span className="sr-only">Görüntüle</span>
                            </Link>
                          </Button>
                          <Button variant="ghost" size="sm" asChild>
                            <Link href={`/admin/testimonials/${testimonial._id || testimonial.id}/edit`}>
                              <Pencil className="h-4 w-4" />
                              <span className="sr-only">Düzenle</span>
                            </Link>
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="text-red-500 hover:text-red-700 hover:bg-red-50"
                            onClick={() => handleDeleteClick(testimonial)}
                          >
                            <Trash className="h-4 w-4" />
                            <span className="sr-only">Sil</span>
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
        <CardFooter className="flex justify-between">
          <p className="text-sm text-gray-500">Toplam {testimonials.length} yorum</p>
        </CardFooter>
      </Card>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Yorumu Silmek İstediğinize Emin misiniz?</AlertDialogTitle>
            <AlertDialogDescription>
              Bu işlem geri alınamaz. Bu yorum kalıcı olarak silinecektir.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>İptal</AlertDialogCancel>
            <AlertDialogAction className="bg-red-500 hover:bg-red-600" onClick={handleDeleteConfirm}>
              Sil
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}
