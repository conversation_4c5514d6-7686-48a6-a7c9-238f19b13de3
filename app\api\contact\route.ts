import { NextResponse } from "next/server"
import dbConnect from "@/lib/db-connect"
import Contact from "@/lib/models/contact"
import { handleApiError } from "@/lib/api-utils"

export async function POST(request: Request) {
  try {
    await dbConnect()

    const body = await request.json()
    const { name, email, phone, subject, message } = body

    // Validation
    if (!name || !email || !subject || !message) {
      return NextResponse.json(
        { success: false, error: "Ad, e-posta, konu ve mesaj alanları gereklidir" },
        { status: 400 }
      )
    }

    const contact = await Contact.create({
      name,
      email,
      phone,
      subject,
      message,
    })

    return NextResponse.json({ success: true, data: contact }, { status: 201 })
  } catch (error) {
    console.error("Error creating contact:", error)
    const errorResponse = await handleApiError(error)
    return NextResponse.json({ success: false, error: errorResponse.error }, { status: errorResponse.status })
  }
}
