import { NextResponse } from "next/server"
import dbConnect from "@/lib/db-connect"
import Program from "@/lib/models/program"

export async function GET(request: Request, { params }: { params: { slug: string } }) {
  try {
    await dbConnect()

    const { slug } = await params

    // Slug ile program ara (slug veya _id ile)
    let program = await Program.findOne({
      slug: slug,
      isActive: true
    })

    // Slug ile bulamazsa _id ile dene
    if (!program && slug.match(/^[0-9a-fA-F]{24}$/)) {
      program = await Program.findOne({
        _id: slug,
        isActive: true
      })
    }

    if (!program) {
      return NextResponse.json({ success: false, error: "Program bulunamadı" }, { status: 404 })
    }

    return NextResponse.json({ success: true, data: program })
  } catch (error) {
    console.error("Error fetching public program:", error)
    return NextResponse.json({ success: false, error: "Program yüklenirken hata olu<PERSON>tu" }, { status: 500 })
  }
}
