import { NextResponse } from "next/server"
import dbConnect from "@/lib/db-connect"
import Program from "@/lib/models/program"

export async function GET(request: Request, { params }: { params: { slug: string } }) {
  try {
    await dbConnect()

    const program = await Program.findOne({ slug: params.slug, isActive: true })

    if (!program) {
      return NextResponse.json({ success: false, error: "Program bulunamadı" }, { status: 404 })
    }

    return NextResponse.json({ success: true, data: program })
  } catch (error) {
    console.error("Error fetching public program:", error)
    return NextResponse.json({ success: false, error: "Program yüklenirken hata oluştu" }, { status: 500 })
  }
}
