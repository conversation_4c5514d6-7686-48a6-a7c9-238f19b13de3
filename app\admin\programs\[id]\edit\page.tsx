"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { useToast } from "@/components/ui/use-toast"
import { ArrowLeft, Save, Trash } from "lucide-react"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"

export default function EditProgramPage({ params }: { params: { id: string } }) {
  const router = useRouter()
  const { toast } = useToast()
  const [isLoading, setIsLoading] = useState(false)
  const [isDeleting, setIsDeleting] = useState(false)
  const [isLoadingData, setIsLoadingData] = useState(true)
  const [formData, setFormData] = useState({
    title: "",
    slug: "",
    description: "",
    fullDescription: "",
    age: "",
    price: "",
    duration: "",
    schedule: "",
    image: "",
    icon: "",
    features: "",
    isActive: true,
  })

  useEffect(() => {
    const fetchProgram = async () => {
      try {
        const response = await fetch(`/api/admin/programs/${params.id}`)
        const data = await response.json()

        if (data.success) {
          const program = data.data
          setFormData({
            title: program.title || "",
            slug: program.slug || "",
            description: program.description || "",
            fullDescription: program.fullDescription || "",
            age: program.age || "",
            price: program.price || "",
            duration: program.duration || "",
            schedule: program.schedule || "",
            image: program.image || "",
            icon: program.icon || "",
            features: Array.isArray(program.features) ? program.features.join("\n") : "",
            isActive: program.isActive ?? true,
          })
        } else {
          toast({
            title: "Hata",
            description: "Program bulunamadı.",
            variant: "destructive",
          })
          router.push("/admin/programs")
        }
      } catch (error) {
        console.error("Error fetching program:", error)
        toast({
          title: "Hata",
          description: "Program yüklenirken bir hata oluştu.",
          variant: "destructive",
        })
      } finally {
        setIsLoadingData(false)
      }
    }

    fetchProgram()
  }, [params.id, router, toast])

  const handleChange = (e) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleSwitchChange = (name, checked) => {
    setFormData((prev) => ({ ...prev, [name]: checked }))
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    setIsLoading(true)

    try {
      const response = await fetch(`/api/admin/programs/${params.id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          ...formData,
          features: formData.features.split("\n").filter((f) => f.trim()),
        }),
      })

      const data = await response.json()

      if (data.success) {
        toast({
          title: "Program Güncellendi",
          description: "Program başarıyla güncellendi.",
        })
        router.push("/admin/programs")
      } else {
        toast({
          title: "Hata",
          description: data.error || "Program güncellenirken bir hata oluştu.",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("Error updating program:", error)
      toast({
        title: "Hata",
        description: "Program güncellenirken bir hata oluştu.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleDelete = async () => {
    setIsDeleting(true)

    try {
      const response = await fetch(`/api/admin/programs/${params.id}`, {
        method: "DELETE",
      })

      const data = await response.json()

      if (data.success) {
        toast({
          title: "Program Silindi",
          description: "Program başarıyla silindi.",
        })
        router.push("/admin/programs")
      } else {
        toast({
          title: "Hata",
          description: data.error || "Program silinirken bir hata oluştu.",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("Error deleting program:", error)
      toast({
        title: "Hata",
        description: "Program silinirken bir hata oluştu.",
        variant: "destructive",
      })
    } finally {
      setIsDeleting(false)
    }
  }

  if (isLoadingData) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-sky-500 mx-auto"></div>
          <p className="mt-4 text-gray-600">Program yükleniyor...</p>
        </div>
      </div>
    )
  }

  return (
    <div>
      <div className="flex items-center gap-4 mb-8">
        <Button variant="ghost" size="sm" asChild>
          <Link href="/admin/programs">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Geri
          </Link>
        </Button>
        <h1 className="text-3xl font-bold">Program Düzenle</h1>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Program Bilgileri</CardTitle>
          <CardDescription>Program bilgilerini düzenleyin.</CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="title">Program Adı</Label>
                <Input
                  id="title"
                  name="title"
                  value={formData.title}
                  onChange={handleChange}
                  placeholder="UA1 - Giriş Seviyesi"
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="slug">URL Slug</Label>
                <Input
                  id="slug"
                  name="slug"
                  value={formData.slug}
                  onChange={handleChange}
                  placeholder="ua1"
                  required
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Açıklama</Label>
              <Textarea
                id="description"
                name="description"
                value={formData.description}
                onChange={handleChange}
                placeholder="Program açıklaması..."
                rows={4}
                required
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="space-y-2">
                <Label htmlFor="age">Yaş Grubu</Label>
                <Input
                  id="age"
                  name="age"
                  value={formData.age}
                  onChange={handleChange}
                  placeholder="6-8 yaş"
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="price">Fiyat</Label>
                <Input
                  id="price"
                  name="price"
                  value={formData.price}
                  onChange={handleChange}
                  placeholder="3.500 TL"
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="duration">Süre</Label>
                <Input
                  id="duration"
                  name="duration"
                  value={formData.duration}
                  onChange={handleChange}
                  placeholder="8 hafta"
                  required
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="features">Özellikler (Her satıra bir özellik)</Label>
              <Textarea
                id="features"
                name="features"
                value={formData.features}
                onChange={handleChange}
                placeholder="Temel uçuş prensipleri&#10;Güvenlik kuralları&#10;Basit kontroller"
                rows={6}
              />
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="isActive"
                checked={formData.isActive}
                onCheckedChange={(checked) => handleSwitchChange("isActive", checked)}
              />
              <Label htmlFor="isActive">Aktif</Label>
            </div>

            <div className="flex gap-4">
              <Button type="submit" disabled={isLoading} className="bg-sky-500 hover:bg-sky-600">
                <Save className="h-4 w-4 mr-2" />
                {isLoading ? "Kaydediliyor..." : "Kaydet"}
              </Button>
              <Button type="button" variant="outline" asChild>
                <Link href="/admin/programs">İptal</Link>
              </Button>
              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <Button type="button" variant="destructive" disabled={isDeleting}>
                    <Trash className="h-4 w-4 mr-2" />
                    {isDeleting ? "Siliniyor..." : "Sil"}
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>Programı Silmek İstediğinize Emin misiniz?</AlertDialogTitle>
                    <AlertDialogDescription>
                      Bu işlem geri alınamaz. Bu program kalıcı olarak silinecek ve tüm ilişkili veriler kaldırılacaktır.
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>İptal</AlertDialogCancel>
                    <AlertDialogAction className="bg-red-500 hover:bg-red-600" onClick={handleDelete}>
                      Sil
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  )
}
