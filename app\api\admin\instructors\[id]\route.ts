import { NextResponse } from "next/server"
import dbConnect from "@/lib/db-connect"
import Instructor from "@/lib/models/instructor"
import { handleApiError } from "@/lib/api-utils"

export async function GET(request: Request, { params }: { params: { id: string } }) {
  try {
    await dbConnect()

    const instructor = await Instructor.findById(params.id)

    if (!instructor) {
      return NextResponse.json({ success: false, error: "Eğitmen bulunamadı" }, { status: 404 })
    }

    return NextResponse.json({ success: true, data: instructor })
  } catch (error) {
    console.error("Error fetching instructor:", error)
    const errorResponse = await handleApiError(error)
    return NextResponse.json({ success: false, error: errorResponse.error }, { status: errorResponse.status })
  }
}

export async function PUT(request: Request, { params }: { params: { id: string } }) {
  try {
    const body = await request.json()

    await dbConnect()

    const instructor = await Instructor.findByIdAndUpdate(params.id, body, {
      new: true,
      runValidators: true,
    })

    if (!instructor) {
      return NextResponse.json({ success: false, error: "Eğitmen bulunamadı" }, { status: 404 })
    }

    return NextResponse.json({ success: true, data: instructor })
  } catch (error) {
    console.error("Error updating instructor:", error)
    const errorResponse = await handleApiError(error)
    return NextResponse.json({ success: false, error: errorResponse.error }, { status: errorResponse.status })
  }
}

export async function DELETE(request: Request, { params }: { params: { id: string } }) {
  try {
    await dbConnect()

    const instructor = await Instructor.findByIdAndDelete(params.id)

    if (!instructor) {
      return NextResponse.json({ success: false, error: "Eğitmen bulunamadı" }, { status: 404 })
    }

    return NextResponse.json({ success: true, message: "Eğitmen başarıyla silindi" })
  } catch (error) {
    console.error("Error deleting instructor:", error)
    const errorResponse = await handleApiError(error)
    return NextResponse.json({ success: false, error: errorResponse.error }, { status: errorResponse.status })
  }
}
