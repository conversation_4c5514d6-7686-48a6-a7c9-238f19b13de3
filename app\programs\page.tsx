"use client"

import Link from "next/link"
import Image from "next/image"
import { motion } from "framer-motion"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { useEffect, useState } from "react"

export default function ProgramsPage() {
  const [programs, setPrograms] = useState([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchPrograms = async () => {
      try {
        const response = await fetch('/api/public/programs')
        const data = await response.json()

        if (data.success) {
          setPrograms(data.data)
        }
      } catch (error) {
        console.error('Error fetching programs:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchPrograms()
  }, [])

  // Fallback static data
  const fallbackPrograms = [
    {
      id: "ua1",
      title: "UA1 - <PERSON><PERSON><PERSON> Seviyesi",
      description: "Drone teknolojisine ilk adım. Te<PERSON> uçuş prensipleri, güvenlik kuralları ve basit kontroller.",
      age: "6-8 yaş",
      duration: "8 Hafta (Haftada 2 saat)",
      price: "3.500 TL",
      image: "/program-ua1.jpg",
      icon: "🚁",
      features: [
        "Drone'un temel parçaları ve çalışma prensipleri",
        "Güvenli uçuş kuralları ve düzenlemeler",
        "Temel uçuş manevraları ve kontroller",
        "Basit görevler ve oyunlar",
        "Takım çalışması ve iletişim becerileri",
      ],
    },
    {
      id: "ua2",
      title: "UA2 - Orta Seviye",
      description: "Daha karmaşık uçuş teknikleri, basit kodlama ve drone parçalarının tanıtımı.",
      age: "9-11 yaş",
      duration: "10 Hafta (Haftada 2 saat)",
      price: "4.500 TL",
      image: "/program-ua2.jpg",
      icon: "⚙️",
      features: [
        "İleri uçuş teknikleri ve manevralar",
        "Drone parçalarının detaylı incelenmesi",
        "Basit kodlama ve otonom uçuş",
        "Hava koşullarına göre uçuş stratejileri",
        "Fotoğraf ve video çekim teknikleri",
      ],
    },
    {
      id: "ua3",
      title: "UA3 - İleri Seviye",
      description: "Drone programlama, 3D tasarım, yarışma hazırlıkları ve proje geliştirme.",
      age: "12-14 yaş",
      duration: "12 Hafta (Haftada 3 saat)",
      price: "5.500 TL",
      image: "/program-ua3.jpg",
      icon: "💻",
      features: [
        "İleri seviye programlama ve otonom uçuş",
        "3D tasarım ve drone parçaları üretimi",
        "Sensör entegrasyonu ve veri analizi",
        "Yarışma stratejileri ve hazırlık",
        "Kapsamlı proje geliştirme ve sunum",
      ],
    },
  ]

  const displayPrograms = programs.length > 0 ? programs : fallbackPrograms
  return (
    <div>
      {/* Hero Section */}
      <section className="relative py-20 bg-sky-600 text-white">
        <div className="container">
          <div className="max-w-3xl">
            <h1 className="text-4xl font-bold tracking-tight sm:text-5xl">Eğitim Programlarımız</h1>
            <p className="mt-6 text-xl text-white/90">
              Farklı yaş grupları ve beceri seviyelerine göre tasarlanmış programlarımızla çocukların drone
              teknolojisini keşfetmelerini sağlıyoruz.
            </p>
          </div>
        </div>
      </section>

      {/* Programs Overview */}
      <section className="py-20 bg-white">
        <div className="container">
          <div className="grid md:grid-cols-3 gap-8">
            {loading ? (
              [...Array(3)].map((_, index) => (
                <div key={index} className="h-96 bg-gray-200 rounded-lg animate-pulse"></div>
              ))
            ) : (
              displayPrograms.map((program, index) => (
                <motion.div
                  key={program._id || program.id || index}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  whileHover={{ y: -10 }}
                  className="h-full"
                >
                  <Card className="h-full border-2 border-sky-100 hover:border-sky-300 transition-colors">
                    <div className="relative h-48 w-full overflow-hidden rounded-t-lg">
                      <Image
                        src={program.image || "/placeholder.svg"}
                        alt={program.title}
                        fill
                        className="object-cover"
                      />
                    </div>
                    <CardHeader>
                      <div className="text-4xl mb-4">{program.icon || "🚁"}</div>
                      <CardTitle>{program.title}</CardTitle>
                      <CardDescription>
                        <div className="space-y-1 mt-2">
                          <p>Yaş Grubu: {program.age}</p>
                          <p>Süre: {program.duration}</p>
                          <p>Ücret: {program.price}</p>
                        </div>
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <p className="mb-4">{program.description}</p>
                      {program.features && program.features.length > 0 && (
                        <>
                          <h4 className="font-semibold mb-2">Program İçeriği:</h4>
                          <ul className="list-disc pl-5 space-y-1">
                            {program.features.map((feature, i) => (
                              <li key={i} className="text-sm text-gray-600">
                                {feature}
                              </li>
                            ))}
                          </ul>
                        </>
                      )}
                    </CardContent>
                    <CardFooter>
                      <div className="w-full space-y-2">
                        <Button variant="outline" className="w-full" asChild>
                          <Link href={`/programs/${program.slug || program.id}`}>Detayları Gör</Link>
                        </Button>
                        <Button className="w-full bg-sky-500 hover:bg-sky-600" asChild>
                          <Link href="/register">Kayıt Ol</Link>
                        </Button>
                      </div>
                    </CardFooter>
                  </Card>
                </motion.div>
              ))
            )}
          </div>
        </div>
      </section>

      {/* Common Questions */}
      <section className="py-20 bg-sky-50">
        <div className="container">
          <div className="text-center max-w-3xl mx-auto mb-16">
            <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">Sıkça Sorulan Sorular</h2>
            <p className="mt-4 text-lg text-gray-600">
              Eğitim programlarımız hakkında en çok sorulan sorular ve cevapları.
            </p>
          </div>
          <div className="max-w-3xl mx-auto">
            {[
              {
                question: "Eğitimlere katılmak için ön bilgi gerekiyor mu?",
                answer:
                  "Hayır, UA1 programımız hiçbir ön bilgi gerektirmez. UA2 ve UA3 programları için ise bir önceki seviyeyi tamamlamış olmak veya eşdeğer bilgiye sahip olmak gerekir.",
              },
              {
                question: "Eğitimler için ekipman sağlıyor musunuz?",
                answer:
                  "Evet, tüm eğitimlerimizde gerekli drone'lar ve ekipmanlar tarafımızdan sağlanmaktadır. Öğrencilerin herhangi bir ekipman getirmesine gerek yoktur.",
              },
              {
                question: "Eğitimler nerede gerçekleşiyor?",
                answer:
                  "Eğitimlerimiz Şişli'deki atölye merkezimizde gerçekleşmektedir. Hava koşullarının uygun olduğu durumlarda, bazı uçuş pratikleri açık alanda yapılabilir.",
              },
              {
                question: "Çocuğum farklı bir yaş grubunda, yine de katılabilir mi?",
                answer:
                  "Yaş grupları genel bir rehberdir. Çocuğunuzun ilgi alanları ve yeteneklerine göre farklı bir seviyeye yerleştirilmesi mümkün olabilir. Bunun için bizimle iletişime geçebilirsiniz.",
              },
              {
                question: "Eğitim sonunda sertifika veriliyor mu?",
                answer:
                  "Evet, tüm programlarımızı başarıyla tamamlayan öğrencilere sertifika verilmektedir. Bu sertifikalar, çocuğunuzun kazandığı becerileri belgelemektedir.",
              },
            ].map((faq, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="mb-6"
              >
                <h3 className="text-xl font-semibold mb-2">{faq.question}</h3>
                <p className="text-gray-600">{faq.answer}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-sky-600 text-white">
        <div className="container">
          <div className="text-center max-w-3xl mx-auto">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">Eğitimlerimize Katılın</h2>
            <p className="mt-4 text-xl text-white/90">
              Çocuğunuzun geleceğine yatırım yapın. Drone teknolojisi ve havacılık bilimi ile tanıştırın.
            </p>
            <div className="mt-10 flex justify-center">
              <Button size="lg" variant="outline" className="text-white border-white hover:bg-white/10" asChild>
                <Link href="/register">Şimdi Kayıt Ol</Link>
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}
