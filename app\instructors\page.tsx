"use client"

import Image from "next/image"
import { motion } from "framer-motion"
import { Card, CardContent } from "@/components/ui/card"
import { Linkedin, Twitter, Globe } from "lucide-react"

const instructors = [
  {
    id: 1,
    name: "<PERSON><PERSON>",
    role: "<PERSON><PERSON> Eğitmen",
    bio: "10 yıllık drone pilotu ve eğitmeni. Mühendislik mezunu ve birçok ulusal yarışmada ödül sahibi. Çocuklara drone teknolojisini öğretme konusunda uzmanlaşmış.",
    image: "/instructor-1.jpg",
    expertise: ["Drone Pilotluğu", "<PERSON><PERSON><PERSON>ş Mekaniği", "Yarışma Stratejileri"],
    social: {
      linkedin: "#",
      twitter: "#",
      website: "#",
    },
  },
  {
    id: 2,
    name: "<PERSON><PERSON><PERSON><PERSON>",
    role: "STEM Eğitmeni",
    bio: "Eğitim teknolojileri uzmanı ve çocuk gelişimi danışmanı. 8 yıllık eğitmenlik deney<PERSON><PERSON> ile STEM eğitiminde uzmanlaşmış. Çocukların yaratıcılığını ve problem çözme becerilerini geliştirmeye odaklanıyor.",
    image: "/instructor-2.jpg",
    expertise: ["STEM Eğitimi", "Çocuk Gelişimi", "Eğitim Teknolojileri"],
    social: {
      linkedin: "#",
      twitter: "#",
    },
  },
  {
    id: 3,
    name: "Mehmet Demir",
    role: "Kodlama Eğitmeni",
    bio: "Yazılım mühendisi ve robotik yarışmaları jüri üyesi. Çocuklara kodlama ve robotik öğretme konusunda 6 yıllık deneyime sahip. Drone programlama ve otonom uçuş sistemleri konusunda uzman.",
    image: "/instructor-3.jpg",
    expertise: ["Python Programlama", "Drone Kodlama", "Robotik"],
    social: {
      linkedin: "#",
      website: "#",
    },
  },
  {
    id: 4,
    name: "Zeynep Şahin",
    role: "3D Tasarım Eğitmeni",
    bio: "Endüstriyel tasarımcı ve maker hareketi savunucusu. 3D modelleme ve baskı konusunda uzman. Çocuklara yaratıcı tasarım becerilerini kazandırmaya odaklanıyor.",
    image: "/instructor-4.jpg",
    expertise: ["3D Modelleme", "Endüstriyel Tasarım", "Maker Kültürü"],
    social: {
      linkedin: "#",
      twitter: "#",
      website: "#",
    },
  },
  {
    id: 5,
    name: "Can Özkan",
    role: "Havacılık Eğitmeni",
    bio: "Havacılık mühendisi ve lisanslı drone pilotu. Havacılık tarihi ve teknolojisi konusunda derin bilgiye sahip. Çocuklara havacılık bilimini sevdirmeyi amaçlıyor.",
    image: "/instructor-5.jpg",
    expertise: ["Havacılık Bilimi", "Drone Teknolojisi", "Aerodinamik"],
    social: {
      linkedin: "#",
    },
  },
  {
    id: 6,
    name: "Elif Yıldırım",
    role: "Proje Geliştirme Eğitmeni",
    bio: "Proje yönetimi uzmanı ve eğitimci. Çocukların proje geliştirme ve sunum becerilerini geliştirmeye odaklanıyor. Ulusal ve uluslararası birçok eğitim projesinde yer almış.",
    image: "/instructor-6.jpg",
    expertise: ["Proje Yönetimi", "Sunum Teknikleri", "Takım Çalışması"],
    social: {
      linkedin: "#",
      twitter: "#",
    },
  },
]

export default function InstructorsPage() {
  return (
    <div>
      {/* Hero Section */}
      <section className="relative py-20 bg-sky-600 text-white">
        <div className="container">
          <div className="max-w-3xl">
            <h1 className="text-4xl font-bold tracking-tight sm:text-5xl">Eğitmenlerimiz</h1>
            <p className="mt-6 text-xl text-white/90">
              Alanında uzman, deneyimli ve çocuklarla çalışmayı seven eğitmenlerimizle tanışın.
            </p>
          </div>
        </div>
      </section>

      {/* Instructors */}
      <section className="py-20 bg-white">
        <div className="container">
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {instructors.map((instructor, index) => (
              <motion.div
                key={instructor.id}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                whileHover={{ y: -10 }}
              >
                <Card className="h-full overflow-hidden">
                  <div className="relative h-80 w-full overflow-hidden">
                    <Image
                      src={instructor.image || "/placeholder.svg"}
                      alt={instructor.name}
                      fill
                      className="object-cover transition-transform hover:scale-105"
                    />
                  </div>
                  <CardContent className="p-6">
                    <h3 className="text-xl font-semibold">{instructor.name}</h3>
                    <p className="text-sky-600 mb-4">{instructor.role}</p>
                    <p className="text-gray-600 mb-4">{instructor.bio}</p>
                    <div className="mb-4">
                      <h4 className="font-semibold mb-2">Uzmanlık Alanları:</h4>
                      <div className="flex flex-wrap gap-2">
                        {instructor.expertise.map((skill, i) => (
                          <span key={i} className="bg-sky-100 text-sky-800 text-xs px-2 py-1 rounded-full">
                            {skill}
                          </span>
                        ))}
                      </div>
                    </div>
                    <div className="flex gap-3">
                      {instructor.social.linkedin && (
                        <a
                          href={instructor.social.linkedin}
                          className="text-gray-500 hover:text-sky-600 transition-colors"
                        >
                          <Linkedin className="h-5 w-5" />
                        </a>
                      )}
                      {instructor.social.twitter && (
                        <a
                          href={instructor.social.twitter}
                          className="text-gray-500 hover:text-sky-600 transition-colors"
                        >
                          <Twitter className="h-5 w-5" />
                        </a>
                      )}
                      {instructor.social.website && (
                        <a
                          href={instructor.social.website}
                          className="text-gray-500 hover:text-sky-600 transition-colors"
                        >
                          <Globe className="h-5 w-5" />
                        </a>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Join Us */}
      <section className="py-16 bg-sky-50">
        <div className="container">
          <div className="text-center max-w-3xl mx-auto">
            <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">Ekibimize Katılın</h2>
            <p className="mt-4 text-lg text-gray-600">
              Drone teknolojisi ve STEM eğitimi konusunda deneyimli misiniz? Çocuklarla çalışmayı seviyor musunuz?
              Ekibimize katılmak için özgeçmişinizi <EMAIL> adresine gönderebilirsiniz.
            </p>
          </div>
        </div>
      </section>
    </div>
  )
}
