"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Checkbox } from "@/components/ui/checkbox"
import { useToast } from "@/components/ui/use-toast"
import { Card, CardContent } from "@/components/ui/card"

export default function RegisterPage() {
  const { toast } = useToast()
  const [formData, setFormData] = useState({
    childName: "",
    childAge: "",
    parentName: "",
    email: "",
    phone: "",
    program: "",
    address: "",
    notes: "",
    termsAccepted: false,
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [step, setStep] = useState(1)

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target
    setFormData((prev) => ({
      ...prev,
      [name]: type === "checkbox" ? checked : value,
    }))
  }

  const handleSelectChange = (name, value) => {
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleCheckboxChange = (checked) => {
    setFormData((prev) => ({ ...prev, termsAccepted: checked }))
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    setIsSubmitting(true)

    try {
      // Simulate API call if in development mode without MongoDB
      if (process.env.NODE_ENV === "development" && !process.env.MONGODB_URI) {
        setTimeout(() => {
          toast({
            title: "Kayıt Başarılı (Geliştirme Modu)",
            description: "Kayıt talebiniz alınmıştır. En kısa sürede sizinle iletişime geçeceğiz.",
          })
          setFormData({
            childName: "",
            childAge: "",
            parentName: "",
            email: "",
            phone: "",
            program: "",
            address: "",
            notes: "",
            termsAccepted: false,
          })
          setIsSubmitting(false)
          setStep(1)
        }, 1500)
        return
      }

      // Real API call
      const response = await fetch("/api/register", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      })

      const data = await response.json()

      if (data.success) {
        toast({
          title: "Kayıt Başarılı",
          description: "Kayıt talebiniz alınmıştır. En kısa sürede sizinle iletişime geçeceğiz.",
        })
        setFormData({
          childName: "",
          childAge: "",
          parentName: "",
          email: "",
          phone: "",
          program: "",
          address: "",
          notes: "",
          termsAccepted: false,
        })
        setStep(1)
      } else {
        toast({
          title: "Hata",
          description: data.error || "Kayıt işlemi sırasında bir hata oluştu. Lütfen tekrar deneyin.",
          variant: "destructive",
        })
      }
    } catch (error) {
      toast({
        title: "Hata",
        description: "Kayıt işlemi sırasında bir hata oluştu. Lütfen tekrar deneyin.",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  const nextStep = () => {
    setStep(step + 1)
  }

  const prevStep = () => {
    setStep(step - 1)
  }

  return (
    <div>
      {/* Hero Section */}
      <section className="relative py-20 bg-sky-600 text-white">
        <div className="container">
          <div className="max-w-3xl">
            <h1 className="text-4xl font-bold tracking-tight sm:text-5xl">Kayıt Ol</h1>
            <p className="mt-6 text-xl text-white/90">
              Çocuğunuzu Uçuş Atölyesi eğitimlerine kaydetmek için formu doldurun.
            </p>
          </div>
        </div>
      </section>

      {/* Registration Form */}
      <section className="py-20 bg-white">
        <div className="container">
          <div className="max-w-3xl mx-auto">
            <div className="mb-8">
              <div className="flex justify-between items-center">
                {[1, 2, 3].map((s) => (
                  <div
                    key={s}
                    className={`flex flex-col items-center ${s < step ? "text-sky-600" : s === step ? "text-sky-600" : "text-gray-400"}`}
                  >
                    <div
                      className={`w-10 h-10 rounded-full flex items-center justify-center mb-2 ${
                        s < step
                          ? "bg-sky-600 text-white"
                          : s === step
                            ? "border-2 border-sky-600 text-sky-600"
                            : "border-2 border-gray-300 text-gray-400"
                      }`}
                    >
                      {s}
                    </div>
                    <span className="text-sm">
                      {s === 1 ? "Kişisel Bilgiler" : s === 2 ? "Program Seçimi" : "Onay"}
                    </span>
                  </div>
                ))}
              </div>
              <div className="relative mt-2">
                <div className="absolute left-0 top-1/2 h-0.5 w-full bg-gray-200 -translate-y-1/2"></div>
                <div
                  className="absolute left-0 top-1/2 h-0.5 bg-sky-600 -translate-y-1/2 transition-all"
                  style={{ width: `${((step - 1) / 2) * 100}%` }}
                ></div>
              </div>
            </div>

            <Card>
              <CardContent className="p-6">
                <form onSubmit={handleSubmit}>
                  {step === 1 && (
                    <motion.div
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      exit={{ opacity: 0, x: 20 }}
                      transition={{ duration: 0.3 }}
                      className="space-y-6"
                    >
                      <h2 className="text-2xl font-bold mb-6">Kişisel Bilgiler</h2>
                      <div className="space-y-2">
                        <Label htmlFor="childName">Çocuğun Adı Soyadı</Label>
                        <Input
                          id="childName"
                          name="childName"
                          value={formData.childName}
                          onChange={handleChange}
                          placeholder="Çocuğun adı soyadı"
                          required
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="childAge">Çocuğun Yaşı</Label>
                        <Input
                          id="childAge"
                          name="childAge"
                          type="number"
                          min="6"
                          max="14"
                          value={formData.childAge}
                          onChange={handleChange}
                          placeholder="Çocuğun yaşı"
                          required
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="parentName">Veli Adı Soyadı</Label>
                        <Input
                          id="parentName"
                          name="parentName"
                          value={formData.parentName}
                          onChange={handleChange}
                          placeholder="Veli adı soyadı"
                          required
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="email">E-posta</Label>
                        <Input
                          id="email"
                          name="email"
                          type="email"
                          value={formData.email}
                          onChange={handleChange}
                          placeholder="<EMAIL>"
                          required
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="phone">Telefon</Label>
                        <Input
                          id="phone"
                          name="phone"
                          value={formData.phone}
                          onChange={handleChange}
                          placeholder="05XX XXX XX XX"
                          required
                        />
                      </div>
                      <div className="flex justify-end">
                        <Button type="button" onClick={nextStep} className="bg-sky-500 hover:bg-sky-600">
                          İleri
                        </Button>
                      </div>
                    </motion.div>
                  )}

                  {step === 2 && (
                    <motion.div
                      initial={{ opacity: 0, x: 20 }}
                      animate={{ opacity: 1, x: 0 }}
                      exit={{ opacity: 0, x: -20 }}
                      transition={{ duration: 0.3 }}
                      className="space-y-6"
                    >
                      <h2 className="text-2xl font-bold mb-6">Program Seçimi</h2>
                      <div className="space-y-2">
                        <Label htmlFor="program">Eğitim Programı</Label>
                        <Select
                          value={formData.program}
                          onValueChange={(value) => handleSelectChange("program", value)}
                          required
                        >
                          <SelectTrigger id="program">
                            <SelectValue placeholder="Program seçin" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="ua1">UA1 - Giriş Seviyesi (6-8 yaş)</SelectItem>
                            <SelectItem value="ua2">UA2 - Orta Seviye (9-11 yaş)</SelectItem>
                            <SelectItem value="ua3">UA3 - İleri Seviye (12-14 yaş)</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="address">Adres</Label>
                        <Textarea
                          id="address"
                          name="address"
                          value={formData.address}
                          onChange={handleChange}
                          placeholder="Adresiniz"
                          className="min-h-[100px]"
                          required
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="notes">Notlar (Opsiyonel)</Label>
                        <Textarea
                          id="notes"
                          name="notes"
                          value={formData.notes}
                          onChange={handleChange}
                          placeholder="Eklemek istediğiniz notlar"
                          className="min-h-[100px]"
                        />
                      </div>
                      <div className="flex justify-between">
                        <Button type="button" onClick={prevStep} variant="outline">
                          Geri
                        </Button>
                        <Button type="button" onClick={nextStep} className="bg-sky-500 hover:bg-sky-600">
                          İleri
                        </Button>
                      </div>
                    </motion.div>
                  )}

                  {step === 3 && (
                    <motion.div
                      initial={{ opacity: 0, x: 20 }}
                      animate={{ opacity: 1, x: 0 }}
                      exit={{ opacity: 0, x: -20 }}
                      transition={{ duration: 0.3 }}
                      className="space-y-6"
                    >
                      <h2 className="text-2xl font-bold mb-6">Kayıt Onayı</h2>
                      <div className="space-y-4 bg-gray-50 p-4 rounded-lg">
                        <div className="grid grid-cols-2 gap-2">
                          <div>
                            <p className="text-sm font-medium text-gray-500">Çocuğun Adı Soyadı</p>
                            <p>{formData.childName}</p>
                          </div>
                          <div>
                            <p className="text-sm font-medium text-gray-500">Çocuğun Yaşı</p>
                            <p>{formData.childAge}</p>
                          </div>
                          <div>
                            <p className="text-sm font-medium text-gray-500">Veli Adı Soyadı</p>
                            <p>{formData.parentName}</p>
                          </div>
                          <div>
                            <p className="text-sm font-medium text-gray-500">E-posta</p>
                            <p>{formData.email}</p>
                          </div>
                          <div>
                            <p className="text-sm font-medium text-gray-500">Telefon</p>
                            <p>{formData.phone}</p>
                          </div>
                          <div>
                            <p className="text-sm font-medium text-gray-500">Program</p>
                            <p>
                              {formData.program === "ua1"
                                ? "UA1 - Giriş Seviyesi"
                                : formData.program === "ua2"
                                  ? "UA2 - Orta Seviye"
                                  : "UA3 - İleri Seviye"}
                            </p>
                          </div>
                        </div>
                        <div>
                          <p className="text-sm font-medium text-gray-500">Adres</p>
                          <p>{formData.address}</p>
                        </div>
                        {formData.notes && (
                          <div>
                            <p className="text-sm font-medium text-gray-500">Notlar</p>
                            <p>{formData.notes}</p>
                          </div>
                        )}
                      </div>
                      <div className="flex items-start space-x-2">
                        <Checkbox
                          id="terms"
                          checked={formData.termsAccepted}
                          onCheckedChange={handleCheckboxChange}
                          required
                        />
                        <div className="grid gap-1.5 leading-none">
                          <label
                            htmlFor="terms"
                            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                          >
                            Kişisel verilerin işlenmesine ilişkin aydınlatma metnini okudum ve kabul ediyorum.
                          </label>
                          <p className="text-sm text-gray-500">
                            Verileriniz eğitim sürecinin yürütülmesi amacıyla işlenecektir.
                          </p>
                        </div>
                      </div>
                      <div className="flex justify-between">
                        <Button type="button" onClick={prevStep} variant="outline">
                          Geri
                        </Button>
                        <Button
                          type="submit"
                          className="bg-sky-500 hover:bg-sky-600"
                          disabled={isSubmitting || !formData.termsAccepted}
                        >
                          {isSubmitting ? "Gönderiliyor..." : "Kaydı Tamamla"}
                        </Button>
                      </div>
                    </motion.div>
                  )}
                </form>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>
    </div>
  )
}
