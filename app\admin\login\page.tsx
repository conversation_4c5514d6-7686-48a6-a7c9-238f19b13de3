"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import Image from "next/image"
import { signIn } from "next-auth/react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { useToast } from "@/components/ui/use-toast"
import { AlertCircle } from "lucide-react"

export default function LoginPage() {
  const router = useRouter()
  const { toast } = useToast()
  const [formData, setFormData] = useState({
    email: "<EMAIL>",
    password: "admin123",
  })
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState("")
  const [isCreatingAdmin, setIsCreatingAdmin] = useState(false)

  const handleChange = (e) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleCreateAdmin = async () => {
    try {
      setIsCreatingAdmin(true)
      const response = await fetch("/api/setup")
      const data = await response.json()

      if (data.success) {
        toast({
          title: "Admin Kullanıcısı Oluşturuldu",
          description: "Admin kullanıcısı başarıyla oluşturuldu. Şimdi giriş yapabilirsiniz.",
        })
      } else {
        toast({
          title: "Bilgi",
          description: data.message || "Admin kullanıcısı zaten mevcut.",
        })
      }
    } catch (error) {
      console.error("Error creating admin:", error)
      toast({
        title: "Hata",
        description: "Admin kullanıcısı oluşturulurken bir hata oluştu.",
        variant: "destructive",
      })
    } finally {
      setIsCreatingAdmin(false)
    }
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    setIsLoading(true)
    setError("")

    try {
      // Geliştirme modunda her zaman giriş yap
      if (process.env.NODE_ENV === "development") {
        // Geliştirme modunda direkt olarak dashboard'a yönlendir
        router.push("/admin/dashboard")
        return
      }

      const result = await signIn("credentials", {
        redirect: false,
        email: formData.email,
        password: formData.password,
      })

      if (result.error) {
        setError("E-posta veya şifre hatalı.")
        setIsLoading(false)
        return
      }

      if (result.ok) {
        router.push("/admin/dashboard")
        toast({
          title: "Giriş Başarılı",
          description: "Admin paneline yönlendiriliyorsunuz.",
        })
      }
    } catch (error) {
      setError("Giriş sırasında bir hata oluştu. Lütfen tekrar deneyin.")
      setIsLoading(false)
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-100 p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="space-y-1">
          <div className="flex justify-center mb-4">
            <Image src="/placeholder.svg?height=60&width=60" alt="Uçuş Atölyesi Logo" width={60} height={60} />
          </div>
          <CardTitle className="text-2xl text-center">Admin Girişi</CardTitle>
          <CardDescription className="text-center">Uçuş Atölyesi yönetim paneline giriş yapın</CardDescription>
        </CardHeader>
        <CardContent>
          {error && (
            <div className="bg-red-50 p-3 rounded-md flex items-start gap-2 mb-4 text-red-700">
              <AlertCircle className="h-5 w-5 mt-0.5 flex-shrink-0" />
              <p>{error}</p>
            </div>
          )}
          <form onSubmit={handleSubmit}>
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="email">E-posta</Label>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  placeholder="<EMAIL>"
                  value={formData.email}
                  onChange={handleChange}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="password">Şifre</Label>
                <Input
                  id="password"
                  name="password"
                  type="password"
                  placeholder="••••••••"
                  value={formData.password}
                  onChange={handleChange}
                  required
                />
              </div>
              <Button type="submit" className="w-full bg-sky-500 hover:bg-sky-600" disabled={isLoading}>
                {isLoading ? "Giriş Yapılıyor..." : "Giriş Yap"}
              </Button>
              <Button
                type="button"
                variant="outline"
                className="w-full mt-2"
                onClick={handleCreateAdmin}
                disabled={isCreatingAdmin}
              >
                {isCreatingAdmin ? "Admin Oluşturuluyor..." : "Admin Kullanıcısı Oluştur"}
              </Button>
            </div>
          </form>
        </CardContent>
        <CardFooter className="flex flex-col gap-2">
          <p className="text-sm text-gray-500 text-center">
            Varsayılan giriş bilgileri:<br />
            E-posta: <EMAIL><br />
            Şifre: admin123
          </p>
          <p className="text-sm text-gray-500 text-center">Şifrenizi mi unuttunuz? Lütfen yönetici ile iletişime geçin.</p>
        </CardFooter>
      </Card>
    </div>
  )
}
