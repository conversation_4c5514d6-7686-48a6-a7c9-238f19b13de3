import { NextResponse } from "next/server"
import dbConnect from "@/lib/db-connect"
import Program from "@/lib/models/program"

export async function GET() {
  try {
    await dbConnect()

    const programs = await Program.find({ isActive: true })
      .select('title slug description age duration price image icon features')
      .sort({ createdAt: -1 })

    return NextResponse.json({ success: true, data: programs })
  } catch (error) {
    console.error("Error fetching public programs:", error)
    return NextResponse.json({ success: false, error: "Programlar yüklenirken hata olu<PERSON>tu" }, { status: 500 })
  }
}
