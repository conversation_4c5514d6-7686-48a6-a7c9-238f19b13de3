import { NextResponse } from "next/server"
import dbConnect from "@/lib/db-connect"
import Contact from "@/lib/models/contact"
import { handleApiError } from "@/lib/api-utils"

export async function GET(request: Request, { params }: { params: { id: string } }) {
  try {
    await dbConnect()

    const { id } = await params
    const contact = await Contact.findById(id)

    if (!contact) {
      return NextResponse.json({ success: false, error: "Mesaj bulunamadı" }, { status: 404 })
    }

    return NextResponse.json({ success: true, data: contact })
  } catch (error) {
    console.error("Error fetching contact:", error)
    const errorResponse = await handleApiError(error)
    return NextResponse.json({ success: false, error: errorResponse.error }, { status: errorResponse.status })
  }
}

export async function PUT(request: Request, { params }: { params: { id: string } }) {
  try {
    await dbConnect()

    const { id } = await params
    const body = await request.json()

    const contact = await Contact.findByIdAndUpdate(id, body, {
      new: true,
      runValidators: true,
    })

    if (!contact) {
      return NextResponse.json({ success: false, error: "Mesaj bulunamadı" }, { status: 404 })
    }

    return NextResponse.json({ success: true, data: contact })
  } catch (error) {
    console.error("Error updating contact:", error)
    const errorResponse = await handleApiError(error)
    return NextResponse.json({ success: false, error: errorResponse.error }, { status: errorResponse.status })
  }
}

export async function DELETE(request: Request, { params }: { params: { id: string } }) {
  try {
    await dbConnect()

    const { id } = await params
    const contact = await Contact.findByIdAndDelete(id)

    if (!contact) {
      return NextResponse.json({ success: false, error: "Mesaj bulunamadı" }, { status: 404 })
    }

    return NextResponse.json({ success: true, message: "Mesaj başarıyla silindi" })
  } catch (error) {
    console.error("Error deleting contact:", error)
    const errorResponse = await handleApiError(error)
    return NextResponse.json({ success: false, error: errorResponse.error }, { status: errorResponse.status })
  }
}
