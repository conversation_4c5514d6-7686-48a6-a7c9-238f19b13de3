import { NextResponse } from "next/server"
import dbConnect from "@/lib/db-connect"
import User from "@/lib/models/user"
import bcrypt from "bcryptjs"

export async function GET() {
  try {
    // Veritabanına bağlan
    await dbConnect()

    // Kullanıcı var mı kontrol et
    const existingUser = await User.findOne({ email: "<EMAIL>" })

    if (existingUser) {
      return NextResponse.json({
        success: true,
        message: "Admin kullanıcısı zaten mevcut",
        user: {
          name: existingUser.name,
          email: existingUser.email,
          role: existingUser.role,
        },
      })
    }

    // Şifreyi hashle
    const salt = await bcrypt.genSalt(10)
    const hashedPassword = await bcrypt.hash("admin123", salt)

    // Yeni admin kullanıcısı oluştur
    const newUser = await User.create({
      name: "<PERSON><PERSON>",
      email: "<EMAIL>",
      password: hashedPassword,
      role: "admin",
      isActive: true,
    })

    return NextResponse.json({
      success: true,
      message: "Admin kullanıcısı başarıyla oluşturuldu",
      user: {
        name: newUser.name,
        email: newUser.email,
        role: newUser.role,
      },
    })
  } catch (error) {
    console.error("Setup error:", error)
    return NextResponse.json({ success: false, error: "Kurulum sırasında bir hata oluştu" }, { status: 500 })
  }
}
