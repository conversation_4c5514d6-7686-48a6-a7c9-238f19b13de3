import { NextResponse } from "next/server"
import dbConnect from "@/lib/db-connect"
import Registration from "@/lib/models/registration"
import { handleApiError } from "@/lib/api-utils"
import { mockCreateRegistration } from "@/lib/mock-db"

export async function POST(request: Request) {
  try {
    const body = await request.json()

    // Connect to the database
    await dbConnect()

    // Create a new registration (real or mock)
    let registration

    if (!process.env.MONGODB_URI) {
      registration = await mockCreateRegistration(body)
    } else {
      registration = await Registration.create(body)
    }

    return NextResponse.json({ success: true, data: registration }, { status: 201 })
  } catch (error) {
    console.error("Registration error:", error)
    const errorResponse = await handleApiError(error)
    return NextResponse.json({ success: false, error: errorResponse.error }, { status: errorResponse.status })
  }
}
