import { NextResponse } from "next/server"
import dbConnect from "@/lib/db-connect"
import Testimonial from "@/lib/models/testimonial"
import { handleApiError } from "@/lib/api-utils"

export async function GET(request: Request) {
  try {
    await dbConnect()

    const testimonials = await Testimonial.find({}).sort({ createdAt: -1 })

    return NextResponse.json({ success: true, data: testimonials })
  } catch (error) {
    console.error("Error fetching testimonials:", error)
    const errorResponse = await handleApiError(error)
    return NextResponse.json({ success: false, error: errorResponse.error }, { status: errorResponse.status })
  }
}

export async function POST(request: Request) {
  try {
    const body = await request.json()

    await dbConnect()

    const testimonial = await Testimonial.create(body)

    return NextResponse.json({ success: true, data: testimonial }, { status: 201 })
  } catch (error) {
    console.error("Error creating testimonial:", error)
    const errorResponse = await handleApiError(error)
    return NextResponse.json({ success: false, error: errorResponse.error }, { status: errorResponse.status })
  }
}
