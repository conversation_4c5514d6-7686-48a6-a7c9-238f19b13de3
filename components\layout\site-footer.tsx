import Link from "next/link"
import { MapPin, Phone, Mail } from "lucide-react"
import { ImageWithFallback } from "@/components/image-with-fallback"

export function SiteFooter() {
  return (
    <footer className="bg-sky-900 dark:bg-gray-900 text-white py-12">
      <div className="container">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div className="space-y-4">
            <Link href="/" className="flex items-center gap-2">
              <ImageWithFallback
                src="/logo-white.png"
                alt="Uçuş Atölyesi Logo"
                width={40}
                height={40}
                type="logo-white"
              />
              <span className="text-xl font-bold">Uçuş Atölyesi</span>
            </Link>
            <p className="text-sky-200 dark:text-gray-300 text-sm">
              Çocuklar için STEM tabanlı drone ve havacılık eğitim merkezi.
            </p>
          </div>
          <div>
            <h3 className="font-semibold text-lg mb-4">H<PERSON><PERSON><PERSON><PERSON> Erişim</h3>
            <ul className="space-y-2">
              <li>
                <Link href="/about" className="text-sky-200 dark:text-gray-300 hover:text-white transition-colors">
                  Hakkımızda
                </Link>
              </li>
              <li>
                <Link href="/programs" className="text-sky-200 dark:text-gray-300 hover:text-white transition-colors">
                  Eğitimler
                </Link>
              </li>
              <li>
                <Link
                  href="/instructors"
                  className="text-sky-200 dark:text-gray-300 hover:text-white transition-colors"
                >
                  Eğitmenler
                </Link>
              </li>
              <li>
                <Link href="/gallery" className="text-sky-200 dark:text-gray-300 hover:text-white transition-colors">
                  Galeri
                </Link>
              </li>
            </ul>
          </div>
          <div>
            <h3 className="font-semibold text-lg mb-4">İletişim</h3>
            <ul className="space-y-2">
              <li className="flex items-center gap-2">
                <MapPin className="h-4 w-4" />
                <span className="text-sky-200 dark:text-gray-300">Şişli, İstanbul</span>
              </li>
              <li className="flex items-center gap-2">
                <Phone className="h-4 w-4" />
                <span className="text-sky-200 dark:text-gray-300">+90 212 123 45 67</span>
              </li>
              <li className="flex items-center gap-2">
                <Mail className="h-4 w-4" />
                <span className="text-sky-200 dark:text-gray-300"><EMAIL></span>
              </li>
            </ul>
          </div>
          <div>
            <h3 className="font-semibold text-lg mb-4">Bizi Takip Edin</h3>
            <div className="flex gap-4">
              <a href="#" className="text-sky-200 dark:text-gray-300 hover:text-white transition-colors">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="h-5 w-5"
                >
                  <path d="M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z" />
                </svg>
              </a>
              <a href="#" className="text-sky-200 dark:text-gray-300 hover:text-white transition-colors">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="h-5 w-5"
                >
                  <rect width="20" height="20" x="2" y="2" rx="5" ry="5" />
                  <path d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z" />
                  <line x1="17.5" x2="17.51" y1="6.5" y2="6.5" />
                </svg>
              </a>
              <a href="#" className="text-sky-200 dark:text-gray-300 hover:text-white transition-colors">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="h-5 w-5"
                >
                  <path d="M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z" />
                </svg>
              </a>
              <a href="#" className="text-sky-200 dark:text-gray-300 hover:text-white transition-colors">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="h-5 w-5"
                >
                  <path d="M23 3a10.9 10.9 0 0 1-3.14 1.53 4.48 4.48 0 0 0-7.86 3v1A10.66 10.66 0 0 1 3 4s-4 9 5 13a11.64 11.64 0 0 1-7 2c9 5 20 0 20-11.5 0-.28-.03-.56-.08-.83A7.72 7.72 0 0 0 23 3z" />
                </svg>
              </a>
            </div>
          </div>
        </div>
        <div className="border-t border-sky-800 dark:border-gray-700 mt-8 pt-8 text-center text-sm text-sky-300 dark:text-gray-400">
          <p>&copy; {new Date().getFullYear()} Uçuş Atölyesi. Tüm hakları saklıdır.</p>
        </div>
      </div>
    </footer>
  )
}
