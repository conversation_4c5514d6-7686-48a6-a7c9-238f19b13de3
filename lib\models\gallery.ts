import mongoose from "mongoose"

// Define the schema
const gallerySchema = new mongoose.Schema(
  {
    title: {
      type: String,
      trim: true,
    },
    category: {
      type: String,
      trim: true,
    },
    image: {
      type: String,
      trim: true,
    },
    description: {
      type: String,
      trim: true,
    },
    isActive: {
      type: Boolean,
      default: true,
    },
  },
  {
    timestamps: true,
  },
)

// Check if the model already exists to prevent overwriting
export default mongoose.models.Gallery || mongoose.model("Gallery", gallerySchema)
