import mongoose from "mongoose"

// Define the schema
const gallerySchema = new mongoose.Schema(
  {
    title: {
      type: String,
      required: [true, "<PERSON><PERSON> başlığı gereklidir"],
      trim: true,
    },
    category: {
      type: String,
      required: [true, "Kategori gereklidir"],
      enum: ["workshop", "event"],
      trim: true,
    },
    image: {
      type: String,
      required: [true, "Görsel gereklidir"],
      trim: true,
    },
    description: {
      type: String,
      required: [true, "Açıklama gereklidir"],
      trim: true,
    },
    isActive: {
      type: Boolean,
      default: true,
    },
  },
  {
    timestamps: true,
  },
)

// Check if the model already exists to prevent overwriting
export default mongoose.models.Gallery || mongoose.model("Gallery", gallerySchema)
