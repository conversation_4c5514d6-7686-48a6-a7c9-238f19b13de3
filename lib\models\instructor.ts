import mongoose from "mongoose"

// Define the schema
const instructorSchema = new mongoose.Schema(
  {
    name: {
      type: String,
      required: [true, "Eğitmen adı gereklidir"],
      trim: true,
    },
    role: {
      type: String,
      required: [true, "Eğitmen rolü gereklidir"],
      trim: true,
    },
    bio: {
      type: String,
      required: [true, "Eğitmen biyografisi gereklidir"],
      trim: true,
    },
    image: {
      type: String,
      required: [true, "Eğitmen fotoğrafı gereklidir"],
      trim: true,
    },
    expertise: {
      type: [String],
      required: [true, "Uzmanlık alanları gereklidir"],
    },
    social: {
      linkedin: {
        type: String,
        trim: true,
      },
      twitter: {
        type: String,
        trim: true,
      },
      website: {
        type: String,
        trim: true,
      },
    },
    isActive: {
      type: Boolean,
      default: true,
    },
  },
  {
    timestamps: true,
  },
)

// Check if the model already exists to prevent overwriting
export default mongoose.models.Instructor || mongoose.model("Instructor", instructorSchema)
