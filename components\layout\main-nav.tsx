"use client"

import * as React from "react"
import Link from "next/link"
import Image from "next/image"
import { usePathname } from "next/navigation"
import { Menu, Home, Info, BookOpen, Users, MessageSquare, Mail } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet"
import { cn } from "@/lib/utils"
import { ThemeToggle } from "@/components/theme-toggle"
import { ImageWithFallback } from "@/components/image-with-fallback"

const navItems = [
  { href: "/", label: "Ana Sayfa" },
  { href: "/about", label: "Hakkımızda" },
  { href: "/programs", label: "Eğitimler" },
  { href: "/gallery", label: "Galeri" },
  { href: "/instructors", label: "Eğitmenler" },
  { href: "/testimonials", label: "Yorumlar" },
  { href: "/contact", label: "<PERSON>let<PERSON>şim" },
]

export function MainNav() {
  const pathname = usePathname()
  const [isOpen, setIsOpen] = React.useState(false)

  return (
    <header className="sticky top-0 z-40 w-full border-b bg-white/80 backdrop-blur-sm">
      <div className="container flex h-16 items-center justify-between">
        <Link href="/" className="flex items-center gap-2">
          <ImageWithFallback src="/logo.png" alt="Uçuş Atölyesi Logo" width={40} height={40} type="logo" />
          <span className="text-xl font-bold text-sky-600">Uçuş Atölyesi</span>
        </Link>
        <nav className="hidden md:flex gap-6">
          {navItems.map((item) => (
            <Link
              key={item.href}
              href={item.href}
              className={cn(
                "text-sm font-medium transition-colors hover:text-sky-600",
                pathname === item.href ? "text-sky-600" : "text-gray-600",
              )}
            >
              {item.label}
            </Link>
          ))}
          <ThemeToggle />
          <Button size="sm" className="bg-sky-500 hover:bg-sky-600" asChild>
            <Link href="/register">Kayıt Ol</Link>
          </Button>
        </nav>
        <Sheet open={isOpen} onOpenChange={setIsOpen}>
          <SheetTrigger asChild>
            <Button variant="outline" size="sm" className="md:hidden">
              <Menu className="h-5 w-5" />
              <span className="sr-only">Menü</span>
            </Button>
          </SheetTrigger>
          <SheetContent side="right">
            <div className="flex flex-col space-y-4 mt-8">
              {navItems.map((item) => (
                <Link
                  key={item.href}
                  href={item.href}
                  className={cn(
                    "flex items-center gap-2 text-base font-medium transition-colors hover:text-sky-600 p-2 rounded-md",
                    pathname === item.href
                      ? "text-sky-600 bg-sky-50 dark:bg-sky-900/20"
                      : "text-gray-600 dark:text-gray-300",
                  )}
                  onClick={() => setIsOpen(false)}
                >
                  {item.href === "/" && <Home className="h-5 w-5" />}
                  {item.href === "/about" && <Info className="h-5 w-5" />}
                  {item.href === "/programs" && <BookOpen className="h-5 w-5" />}
                  {item.href === "/gallery" && <Image className="h-5 w-5" />}
                  {item.href === "/instructors" && <Users className="h-5 w-5" />}
                  {item.href === "/testimonials" && <MessageSquare className="h-5 w-5" />}
                  {item.href === "/contact" && <Mail className="h-5 w-5" />}
                  {item.label}
                </Link>
              ))}
              <div className="flex items-center gap-2 p-2">
                <ThemeToggle />
                <span className="text-sm text-gray-500 dark:text-gray-400">Tema Değiştir</span>
              </div>
              <Button className="mt-4 bg-sky-500 hover:bg-sky-600" asChild>
                <Link href="/register" onClick={() => setIsOpen(false)}>
                  Kayıt Ol
                </Link>
              </Button>
            </div>
          </SheetContent>
        </Sheet>
      </div>
    </header>
  )
}
