import { NextResponse } from "next/server"
import dbConnect from "@/lib/db-connect"
import Program from "@/lib/models/program"
import { handleApiError } from "@/lib/api-utils"

export async function GET(request: Request, { params }: { params: { id: string } }) {
  try {
    await dbConnect()

    const program = await Program.findById(params.id)

    if (!program) {
      return NextResponse.json({ success: false, error: "Program bulunamadı" }, { status: 404 })
    }

    return NextResponse.json({ success: true, data: program })
  } catch (error) {
    console.error("Error fetching program:", error)
    const errorResponse = await handleApiError(error)
    return NextResponse.json({ success: false, error: errorResponse.error }, { status: errorResponse.status })
  }
}

export async function PUT(request: Request, { params }: { params: { id: string } }) {
  try {
    const body = await request.json()

    await dbConnect()

    const program = await Program.findByIdAndUpdate(params.id, body, {
      new: true,
      runValidators: true,
    })

    if (!program) {
      return NextResponse.json({ success: false, error: "Program bulunamadı" }, { status: 404 })
    }

    return NextResponse.json({ success: true, data: program })
  } catch (error) {
    console.error("Error updating program:", error)
    const errorResponse = await handleApiError(error)
    return NextResponse.json({ success: false, error: errorResponse.error }, { status: errorResponse.status })
  }
}

export async function DELETE(request: Request, { params }: { params: { id: string } }) {
  try {
    await dbConnect()

    const program = await Program.findByIdAndDelete(params.id)

    if (!program) {
      return NextResponse.json({ success: false, error: "Program bulunamadı" }, { status: 404 })
    }

    return NextResponse.json({ success: true, message: "Program başarıyla silindi" })
  } catch (error) {
    console.error("Error deleting program:", error)
    const errorResponse = await handleApiError(error)
    return NextResponse.json({ success: false, error: errorResponse.error }, { status: errorResponse.status })
  }
}
