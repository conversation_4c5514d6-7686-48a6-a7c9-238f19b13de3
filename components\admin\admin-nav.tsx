"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { signOut } from "next-auth/react"
import { But<PERSON> } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  LayoutDashboard,
  Users,
  BookOpen,
  ImageIcon,
  MessageSquare,
  UserCircle,
  LogOut,
  Menu,
  ChevronDown,
  Mail,
} from "lucide-react"
import { cn } from "@/lib/utils"
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet"
import { useState } from "react"

const navItems = [
  {
    title: "Dashboard",
    href: "/admin/dashboard",
    icon: <LayoutDashboard className="h-5 w-5" />,
  },
  {
    title: "Kayıtlar",
    href: "/admin/registrations",
    icon: <Users className="h-5 w-5" />,
  },
  {
    title: "Programlar",
    href: "/admin/programs",
    icon: <BookOpen className="h-5 w-5" />,
  },
  {
    title: "Eğitmenler",
    href: "/admin/instructors",
    icon: <UserCircle className="h-5 w-5" />,
  },
  {
    title: "Galeri",
    href: "/admin/gallery",
    icon: <ImageIcon className="h-5 w-5" />,
  },
  {
    title: "Yorumlar",
    href: "/admin/testimonials",
    icon: <MessageSquare className="h-5 w-5" />,
  },
  {
    title: "İletişim",
    href: "/admin/contacts",
    icon: <Mail className="h-5 w-5" />,
  },
]

export function AdminNav({ user }) {
  const pathname = usePathname()
  const [isOpen, setIsOpen] = useState(false)

  const handleLogout = async () => {
    // Geliştirme modunda sadece ana sayfaya yönlendir
    if (process.env.NODE_ENV === "development") {
      window.location.href = "/"
      return
    }

    // Production modunda NextAuth signOut kullan
    await signOut({ callbackUrl: "/" })
  }

  return (
    <header className="sticky top-0 z-40 w-full border-b bg-white">
      <div className="container flex h-16 items-center justify-between">
        <div className="flex items-center">
          <Link href="/admin/dashboard" className="flex items-center gap-2">
            <span className="text-xl font-bold text-sky-600">Uçuş Atölyesi Admin</span>
          </Link>
        </div>
        <nav className="hidden md:flex items-center gap-6">
          {navItems.map((item) => (
            <Link
              key={item.href}
              href={item.href}
              className={cn(
                "flex items-center gap-1 text-sm font-medium transition-colors hover:text-sky-600",
                pathname === item.href ? "text-sky-600" : "text-gray-600",
              )}
            >
              {item.icon}
              {item.title}
            </Link>
          ))}
        </nav>
        <div className="flex items-center gap-2">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="flex items-center gap-1">
                {user?.name || "Geliştirici"}
                <ChevronDown className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Hesabım</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem asChild>
                <Link href="/admin/profile">Profil</Link>
              </DropdownMenuItem>
              <DropdownMenuItem onClick={handleLogout}>
                <LogOut className="h-4 w-4 mr-2" />
                Çıkış Yap
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
          <Sheet open={isOpen} onOpenChange={setIsOpen}>
            <SheetTrigger asChild>
              <Button variant="outline" size="sm" className="md:hidden">
                <Menu className="h-5 w-5" />
                <span className="sr-only">Menü</span>
              </Button>
            </SheetTrigger>
            <SheetContent side="right">
              <div className="flex flex-col space-y-4 mt-8">
                {navItems.map((item) => (
                  <Link
                    key={item.href}
                    href={item.href}
                    className={cn(
                      "flex items-center gap-2 text-base font-medium transition-colors hover:text-sky-600",
                      pathname === item.href ? "text-sky-600" : "text-gray-600",
                    )}
                    onClick={() => setIsOpen(false)}
                  >
                    {item.icon}
                    {item.title}
                  </Link>
                ))}
                <Button
                  variant="ghost"
                  className="flex items-center justify-start gap-2 text-base font-medium text-red-500 hover:text-red-600 hover:bg-red-50 px-0"
                  onClick={handleLogout}
                >
                  <LogOut className="h-5 w-5" />
                  Çıkış Yap
                </Button>
              </div>
            </SheetContent>
          </Sheet>
        </div>
      </div>
    </header>
  )
}
