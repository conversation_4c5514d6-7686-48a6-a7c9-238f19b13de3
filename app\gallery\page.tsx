"use client"

import { useState, useEffect } from "react"
import Image from "next/image"
import { motion } from "framer-motion"
import { Dialog, DialogContent } from "@/components/ui/dialog"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"

export default function GalleryPage() {
  const [galleryItems, setGalleryItems] = useState([])
  const [loading, setLoading] = useState(true)
  const [selectedImage, setSelectedImage] = useState(null)
  const [isOpen, setIsOpen] = useState(false)

  useEffect(() => {
    const fetchGallery = async () => {
      try {
        const response = await fetch('/api/public/gallery')
        const data = await response.json()

        if (data.success) {
          setGalleryItems(data.data)
        }
      } catch (error) {
        console.error('Error fetching gallery:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchGallery()
  }, [])

  // Fallback static data
  const fallbackGalleryItems = [
  {
    id: 1,
    title: "Drone Uçuş Eğitimi",
    category: "workshop",
    image: "/gallery-1.jpg",
    description: "UA1 öğrencilerimizin ilk uçuş deneyimleri.",
  },
  {
    id: 2,
    title: "Drone Yarışması",
    category: "event",
    image: "/gallery-2.jpg",
    description: "Yıl sonu drone yarışmamızdan heyecanlı anlar.",
  },
  {
    id: 3,
    title: "Açık Hava Eğitimi",
    category: "workshop",
    image: "/gallery-3.jpg",
    description: "Açık havada drone uçuş teknikleri eğitimi.",
  },
  {
    id: 4,
    title: "3D Tasarım Atölyesi",
    category: "workshop",
    image: "/gallery-4.jpg",
    description: "UA3 öğrencilerimizin 3D drone parçaları tasarım çalışmaları.",
  },
  {
    id: 5,
    title: "Veli Bilgilendirme Günü",
    category: "event",
    image: "/gallery-5.jpg",
    description: "Velilerimize yönelik bilgilendirme ve demo günü etkinliği.",
  },
  {
    id: 6,
    title: "Kodlama Eğitimi",
    category: "workshop",
    image: "/gallery-6.jpg",
    description: "Drone programlama ve otonom uçuş eğitimi.",
  },
  {
    id: 7,
    title: "Mezuniyet Töreni",
    category: "event",
    image: "/gallery-7.jpg",
    description: "UA2 ve UA3 öğrencilerimizin mezuniyet töreni.",
  },
  {
    id: 8,
    title: "Drone İle Fotoğrafçılık",
    category: "workshop",
    image: "/gallery-8.jpg",
    description: "Havadan fotoğraf ve video çekim teknikleri eğitimi.",
  },
  {
    id: 9,
    title: "Teknoloji Festivali",
    category: "event",
    image: "/gallery-9.jpg",
    description: "Teknoloji festivalinde drone gösterilerimiz.",
  },
  {
    id: 10,
    title: "Sensör Entegrasyonu",
    category: "workshop",
    image: "/gallery-10.jpg",
    description: "Drone'lara farklı sensörlerin entegrasyonu çalışmaları.",
  },
  {
    id: 11,
    title: "Takım Çalışması",
    category: "workshop",
    image: "/gallery-11.jpg",
    description: "Öğrencilerimizin grup projesi çalışmaları.",
  },
  {
    id: 12,
    title: "Yaz Kampı",
    category: "event",
    image: "/gallery-12.jpg",
    description: "Yaz dönemi yoğunlaştırılmış drone eğitim kampımız.",
  },
  ]

  const displayGalleryItems = galleryItems.length > 0 ? galleryItems : fallbackGalleryItems

  const openImage = (image) => {
    setSelectedImage(image)
    setIsOpen(true)
  }

  return (
    <div>
      {/* Hero Section */}
      <section className="relative py-20 bg-sky-600 text-white">
        <div className="container">
          <div className="max-w-3xl">
            <h1 className="text-4xl font-bold tracking-tight sm:text-5xl">Galeri</h1>
            <p className="mt-6 text-xl text-white/90">
              Atölye çalışmalarımız, etkinliklerimiz ve öğrencilerimizin başarılarından kareler.
            </p>
          </div>
        </div>
      </section>

      {/* Gallery */}
      <section className="py-20 bg-white">
        <div className="container">
          <Tabs defaultValue="all" className="w-full">
            <div className="flex justify-center mb-8">
              <TabsList>
                <TabsTrigger value="all">Tümü</TabsTrigger>
                <TabsTrigger value="workshop">Atölyeler</TabsTrigger>
                <TabsTrigger value="event">Etkinlikler</TabsTrigger>
              </TabsList>
            </div>
            <TabsContent value="all">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {loading ? (
                  [...Array(12)].map((_, index) => (
                    <div key={index} className="h-64 bg-gray-200 rounded-lg animate-pulse"></div>
                  ))
                ) : (
                  displayGalleryItems.map((item) => (
                    <GalleryItem key={item._id || item.id} item={item} onClick={() => openImage(item)} />
                  ))
                )}
              </div>
            </TabsContent>
            <TabsContent value="workshop">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {loading ? (
                  [...Array(8)].map((_, index) => (
                    <div key={index} className="h-64 bg-gray-200 rounded-lg animate-pulse"></div>
                  ))
                ) : (
                  displayGalleryItems
                    .filter((item) => item.category === "workshop")
                    .map((item) => (
                      <GalleryItem key={item._id || item.id} item={item} onClick={() => openImage(item)} />
                    ))
                )}
              </div>
            </TabsContent>
            <TabsContent value="event">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {loading ? (
                  [...Array(4)].map((_, index) => (
                    <div key={index} className="h-64 bg-gray-200 rounded-lg animate-pulse"></div>
                  ))
                ) : (
                  displayGalleryItems
                    .filter((item) => item.category === "event")
                    .map((item) => (
                      <GalleryItem key={item._id || item.id} item={item} onClick={() => openImage(item)} />
                    ))
                )}
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </section>

      {/* Image Dialog */}
      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogContent className="max-w-4xl p-0 overflow-hidden bg-transparent border-none">
          {selectedImage && (
            <div className="relative">
              <div className="relative h-[80vh] w-full">
                <Image
                  src={selectedImage.image || "/placeholder.svg"}
                  alt={selectedImage.title}
                  fill
                  className="object-contain"
                />
              </div>
              <div className="absolute bottom-0 left-0 right-0 bg-black/70 p-4 text-white">
                <h3 className="text-xl font-semibold">{selectedImage.title}</h3>
                <p className="text-white/80">{selectedImage.description}</p>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}

function GalleryItem({ item, onClick }) {
  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      whileInView={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.5 }}
      viewport={{ once: true }}
      whileHover={{ y: -5 }}
      className="cursor-pointer group"
      onClick={onClick}
    >
      <div className="relative h-64 overflow-hidden rounded-lg">
        <Image
          src={item.image || "/placeholder.svg"}
          alt={item.title}
          fill
          className="object-cover transition-transform group-hover:scale-105"
        />
        <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent opacity-0 group-hover:opacity-100 transition-opacity flex items-end">
          <div className="p-4 text-white">
            <h3 className="font-semibold">{item.title}</h3>
            <p className="text-sm text-white/80">{item.description}</p>
          </div>
        </div>
      </div>
    </motion.div>
  )
}
