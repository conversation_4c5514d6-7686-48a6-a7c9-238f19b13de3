"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { useToast } from "@/components/ui/use-toast"
import { PlusCircle, Pencil, Trash, Eye } from "lucide-react"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"

export default function ProgramsPage() {
  const { toast } = useToast()
  const [programs, setPrograms] = useState([])
  const [isLoading, setIsLoading] = useState(true)
  const [programToDelete, setProgramToDelete] = useState(null)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)

  useEffect(() => {
    // In a real implementation, this would fetch data from your API
    const fetchData = async () => {
      try {
        // Simulate API call
        setTimeout(() => {
          setPrograms([
            {
              id: 1,
              title: "UA1 - Giriş Seviyesi",
              slug: "ua1",
              description:
                "Drone teknolojisine ilk adım. Temel uçuş prensipleri, güvenlik kuralları ve basit kontroller.",
              age: "6-8 yaş",
              price: "3.500 TL",
              isActive: true,
              createdAt: "2023-01-15T10:30:00Z",
            },
            {
              id: 2,
              title: "UA2 - Orta Seviye",
              slug: "ua2",
              description: "Daha karmaşık uçuş teknikleri, basit kodlama ve drone parçalarının tanıtımı.",
              age: "9-11 yaş",
              price: "4.500 TL",
              isActive: true,
              createdAt: "2023-01-16T14:20:00Z",
            },
            {
              id: 3,
              title: "UA3 - İleri Seviye",
              slug: "ua3",
              description: "Drone programlama, 3D tasarım, yarışma hazırlıkları ve proje geliştirme.",
              age: "12-14 yaş",
              price: "5.500 TL",
              isActive: true,
              createdAt: "2023-01-17T09:15:00Z",
            },
          ])
          setIsLoading(false)
        }, 1000)
      } catch (error) {
        console.error("Error fetching programs:", error)
        setIsLoading(false)
      }
    }

    fetchData()
  }, [])

  const handleDeleteClick = (program) => {
    setProgramToDelete(program)
    setIsDeleteDialogOpen(true)
  }

  const handleDeleteConfirm = async () => {
    try {
      // In a real implementation, this would be an API call
      // Simulate API call
      setTimeout(() => {
        setPrograms((prev) => prev.filter((p) => p.id !== programToDelete.id))
        toast({
          title: "Program Silindi",
          description: `"${programToDelete.title}" programı başarıyla silindi.`,
        })
        setIsDeleteDialogOpen(false)
        setProgramToDelete(null)
      }, 500)
    } catch (error) {
      console.error("Error deleting program:", error)
      toast({
        title: "Hata",
        description: "Program silinirken bir hata oluştu.",
        variant: "destructive",
      })
    }
  }

  const formatDate = (dateString) => {
    const date = new Date(dateString)
    return new Intl.DateTimeFormat("tr-TR", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
    }).format(date)
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-3xl font-bold">Programlar</h1>
        <Button className="bg-sky-500 hover:bg-sky-600" asChild>
          <Link href="/admin/programs/new">
            <PlusCircle className="h-4 w-4 mr-2" />
            Yeni Program
          </Link>
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Program Listesi</CardTitle>
          <CardDescription>Tüm eğitim programlarını görüntüleyin ve yönetin.</CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="text-center py-8">
              <p>Yükleniyor...</p>
            </div>
          ) : programs.length === 0 ? (
            <div className="text-center py-8">
              <p>Henüz program bulunmuyor.</p>
              <Button className="mt-4 bg-sky-500 hover:bg-sky-600" asChild>
                <Link href="/admin/programs/new">
                  <PlusCircle className="h-4 w-4 mr-2" />
                  İlk Programı Ekle
                </Link>
              </Button>
            </div>
          ) : (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Program Adı</TableHead>
                    <TableHead>Yaş Grubu</TableHead>
                    <TableHead>Ücret</TableHead>
                    <TableHead>Durum</TableHead>
                    <TableHead>Oluşturulma</TableHead>
                    <TableHead className="text-right">İşlemler</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {programs.map((program) => (
                    <TableRow key={program.id}>
                      <TableCell className="font-medium">{program.title}</TableCell>
                      <TableCell>{program.age}</TableCell>
                      <TableCell>{program.price}</TableCell>
                      <TableCell>
                        <span
                          className={`px-2 py-1 text-xs rounded-full ${
                            program.isActive ? "bg-green-100 text-green-800" : "bg-gray-100 text-gray-800"
                          }`}
                        >
                          {program.isActive ? "Aktif" : "Pasif"}
                        </span>
                      </TableCell>
                      <TableCell>{formatDate(program.createdAt)}</TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end gap-2">
                          <Button variant="ghost" size="sm" asChild>
                            <Link href={`/programs/${program.slug}`} target="_blank">
                              <Eye className="h-4 w-4" />
                              <span className="sr-only">Görüntüle</span>
                            </Link>
                          </Button>
                          <Button variant="ghost" size="sm" asChild>
                            <Link href={`/admin/programs/${program.id}/edit`}>
                              <Pencil className="h-4 w-4" />
                              <span className="sr-only">Düzenle</span>
                            </Link>
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="text-red-500 hover:text-red-700 hover:bg-red-50"
                            onClick={() => handleDeleteClick(program)}
                          >
                            <Trash className="h-4 w-4" />
                            <span className="sr-only">Sil</span>
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
        <CardFooter className="flex justify-between">
          <p className="text-sm text-gray-500">Toplam {programs.length} program</p>
        </CardFooter>
      </Card>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Programı Silmek İstediğinize Emin misiniz?</AlertDialogTitle>
            <AlertDialogDescription>
              Bu işlem geri alınamaz. Bu program kalıcı olarak silinecek ve tüm ilişkili veriler kaldırılacaktır.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>İptal</AlertDialogCancel>
            <AlertDialogAction className="bg-red-500 hover:bg-red-600" onClick={handleDeleteConfirm}>
              Sil
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}
