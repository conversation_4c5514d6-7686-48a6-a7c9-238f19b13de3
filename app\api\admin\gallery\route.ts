import { NextResponse } from "next/server"
import dbConnect from "@/lib/db-connect"
import Gallery from "@/lib/models/gallery"
import { handleApiError } from "@/lib/api-utils"

export async function GET(request: Request) {
  try {
    await dbConnect()

    const galleryItems = await Gallery.find({}).sort({ createdAt: -1 })

    return NextResponse.json({ success: true, data: galleryItems })
  } catch (error) {
    console.error("Error fetching gallery items:", error)
    const errorResponse = await handleApiError(error)
    return NextResponse.json({ success: false, error: errorResponse.error }, { status: errorResponse.status })
  }
}

export async function POST(request: Request) {
  try {
    const body = await request.json()

    await dbConnect()

    const galleryItem = await Gallery.create(body)

    return NextResponse.json({ success: true, data: galleryItem }, { status: 201 })
  } catch (error) {
    console.error("Error creating gallery item:", error)
    const errorResponse = await handleApiError(error)
    return NextResponse.json({ success: false, error: errorResponse.error }, { status: errorResponse.status })
  }
}
