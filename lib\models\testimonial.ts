import mongoose from "mongoose"

// Define the schema
const testimonialSchema = new mongoose.Schema(
  {
    name: {
      type: String,
      required: [true, "Veli adı gereklidir"],
      trim: true,
    },
    role: {
      type: String,
      trim: true,
    },
    quote: {
      type: String,
      trim: true,
    },
    image: {
      type: String,
      trim: true,
    },
    rating: {
      type: Number,
      min: 1,
      max: 5,
      default: 5,
    },
    program: {
      type: String,
      enum: ["UA1", "UA2", "UA3"],
    },
    isActive: {
      type: Boolean,
      default: true,
    },
  },
  {
    timestamps: true,
  },
)

// Check if the model already exists to prevent overwriting
export default mongoose.models.Testimonial || mongoose.model("Testimonial", testimonialSchema)
