import mongoose from "mongoose"

// Define the schema
const testimonialSchema = new mongoose.Schema(
  {
    name: {
      type: String,
      required: [true, "Veli adı gereklidir"],
      trim: true,
    },
    role: {
      type: String,
      required: [true, "Veli rolü gereklidir"],
      trim: true,
    },
    quote: {
      type: String,
      required: [true, "Yorum gereklidir"],
      trim: true,
    },
    image: {
      type: String,
      required: [true, "Veli fotoğrafı gereklidir"],
      trim: true,
    },
    rating: {
      type: Number,
      required: [true, "Değerlendirme puanı gereklidir"],
      min: 1,
      max: 5,
    },
    program: {
      type: String,
      required: [true, "Program gereklidir"],
      enum: ["UA1", "UA2", "UA3"],
    },
    isActive: {
      type: Boolean,
      default: true,
    },
  },
  {
    timestamps: true,
  },
)

// Check if the model already exists to prevent overwriting
export default mongoose.models.Testimonial || mongoose.model("Testimonial", testimonialSchema)
