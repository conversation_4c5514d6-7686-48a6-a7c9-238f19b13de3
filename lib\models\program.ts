import mongoose from "mongoose"

// Define the schema
const programSchema = new mongoose.Schema(
  {
    title: {
      type: String,
      required: [true, "Program başlığı gereklidir"],
      trim: true,
    },
    slug: {
      type: String,
      required: [true, "Program slug'ı gereklidir"],
      trim: true,
      unique: true,
    },
    description: {
      type: String,
      required: [true, "Program açıklaması gereklidir"],
      trim: true,
    },
    fullDescription: {
      type: String,
      required: [true, "Program detaylı açıklaması gereklidir"],
      trim: true,
    },
    age: {
      type: String,
      required: [true, "Yaş grubu gereklidir"],
      trim: true,
    },
    duration: {
      type: String,
      required: [true, "Süre gereklidir"],
      trim: true,
    },
    schedule: {
      type: String,
      required: [true, "Program zamanı gereklidir"],
      trim: true,
    },
    price: {
      type: String,
      required: [true, "Ücret gereklidir"],
      trim: true,
    },
    image: {
      type: String,
      required: [true, "Görsel gereklidir"],
      trim: true,
    },
    icon: {
      type: String,
      trim: true,
    },
    features: {
      type: [String],
      required: [true, "Özellikler gereklidir"],
    },
    curriculum: [
      {
        week: {
          type: String,
          required: true,
        },
        title: {
          type: String,
          required: true,
        },
        content: {
          type: String,
          required: true,
        },
      },
    ],
    faqs: [
      {
        question: {
          type: String,
          required: true,
        },
        answer: {
          type: String,
          required: true,
        },
      },
    ],
    isActive: {
      type: Boolean,
      default: true,
    },
  },
  {
    timestamps: true,
  },
)

// Check if the model already exists to prevent overwriting
export default mongoose.models.Program || mongoose.model("Program", programSchema)
