"use client"

import { useSession } from "next-auth/react"
import { useRouter, usePathname } from "next/navigation"
import { useEffect } from "react"
import { AdminNav } from "@/components/admin/admin-nav"

export default function AdminLayout({ children }) {
  const { data: session, status } = useSession()
  const router = useRouter()
  const pathname = usePathname()

  useEffect(() => {
    // Geliştirme modunda her zaman admin layout'u göster
    if (process.env.NODE_ENV === "development") {
      return
    }

    // Login sayfasında değilsek ve session yoksa login'e yönlendir
    if (status === "loading") return // Yükleniyor

    if (!session && pathname !== "/admin/login") {
      router.push("/admin/login")
      return
    }

    // Session varsa ama admin değilse ana sayfaya yönlendir
    if (session && session.user?.role !== "admin" && pathname !== "/admin/login") {
      router.push("/")
      return
    }
  }, [session, status, router, pathname])

  // Geliştirme modunda her zaman göster
  if (process.env.NODE_ENV === "development") {
    return (
      <div className="flex min-h-screen flex-col">
        <AdminNav user={{ name: "Geliştirici", role: "admin" }} />
        <div className="flex-1 p-8">{children}</div>
      </div>
    )
  }

  // Login sayfasında layout gösterme
  if (pathname === "/admin/login") {
    return <>{children}</>
  }

  // Yükleniyor durumu
  if (status === "loading") {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-sky-500 mx-auto"></div>
          <p className="mt-4 text-gray-600">Yükleniyor...</p>
        </div>
      </div>
    )
  }

  // Session yoksa veya admin değilse hiçbir şey gösterme
  if (!session || session.user?.role !== "admin") {
    return null
  }

  return (
    <div className="flex min-h-screen flex-col">
      <AdminNav user={session.user} />
      <div className="flex-1 p-8">{children}</div>
    </div>
  )
}
